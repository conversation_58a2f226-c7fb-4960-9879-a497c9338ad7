const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');
const { catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Helper function to map database statuses to frontend statuses
function mapDbStatusToFrontend(dbStatus) {
    const statusMapping = {
        'draft': 'new',
        'submitted': 'new',
        'under_review': 'under_review',
        'approved': 'approved',
        'rejected': 'declined',
        'funded': 'approved'
    };
    return statusMapping[dbStatus] || dbStatus;
}

// Apply authentication and admin authorization to all routes
router.use(authenticate);
router.use(authorize('admin'));

/**
 * PUT /api/admin/applications/bulk/status
 * Bulk update application statuses
 */
router.put('/status', [
    body('applicationIds').isArray().withMessage('Application IDs must be an array'),
    body('applicationIds.*').isUUID().withMessage('Each application ID must be a valid UUID'),
    body('status').isIn(['new', 'under_review', 'approved', 'declined']).withMessage('Invalid status')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { applicationIds, status } = req.body;

    // Map frontend status to database status
    const statusMapping = {
        'new': 'submitted',
        'under_review': 'under_review',
        'approved': 'approved',
        'declined': 'rejected'
    };

    const dbStatus = statusMapping[status];

    // Update applications one by one to avoid type issues
    const updatedApplications = [];

    for (const applicationId of applicationIds) {
        try {
            // Use a more explicit approach to handle the enum type
            const updateQuery = `
                UPDATE applications
                SET status = CAST($1 AS application_status),
                    updated_at = NOW()
                WHERE id = $2
                RETURNING id, status
            `;

            const result = await query(updateQuery, [dbStatus, applicationId]);
            if (result.rows.length > 0) {
                updatedApplications.push(result.rows[0]);
            }
        } catch (error) {
            logger.error('Error updating application status', {
                applicationId,
                dbStatus,
                error: error.message
            });
            // Continue with other applications even if one fails
        }
    }

    // Then update reviewed_at for statuses that need it
    if (['under_review', 'approved', 'rejected'].includes(dbStatus)) {
        for (const applicationId of applicationIds) {
            const reviewedQuery = `
                UPDATE applications
                SET reviewed_at = NOW()
                WHERE id = $1 AND reviewed_at IS NULL
            `;
            await query(reviewedQuery, [applicationId]);
        }
    }

    logger.info('Bulk application status update', {
        applicationIds,
        newStatus: status,
        updatedCount: updatedApplications.length,
        updatedBy: req.user.id,
        timestamp: new Date().toISOString()
    });

    // Map statuses back to frontend format
    const mappedApplications = updatedApplications.map(app => ({
        ...app,
        status: mapDbStatusToFrontend(app.status)
    }));

    res.json({
        status: 'success',
        message: `Successfully updated ${updatedApplications.length} applications`,
        data: {
            updatedApplications: mappedApplications,
            updatedCount: updatedApplications.length
        }
    });
}));

/**
 * DELETE /api/admin/applications/bulk
 * Bulk delete applications
 */
router.delete('/', [
    body('applicationIds').isArray().withMessage('Application IDs must be an array'),
    body('applicationIds.*').isUUID().withMessage('Each application ID must be a valid UUID')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { applicationIds } = req.body;

    // Get application details before deletion for logging
    const placeholders = applicationIds.map((_, index) => `$${index + 1}`).join(',');
    const selectQuery = `
        SELECT a.id, a.status, a.loan_amount, u.email, u.first_name, u.last_name
        FROM applications a
        JOIN users u ON a.user_id = u.id
        WHERE a.id IN (${placeholders})
    `;

    const selectResult = await query(selectQuery, applicationIds);

    // Delete applications (cascade will handle related documents)
    const deleteQuery = `
        DELETE FROM applications
        WHERE id IN (${placeholders})
        RETURNING id
    `;

    const deleteResult = await query(deleteQuery, applicationIds);

    logger.info('Bulk application deletion', {
        applicationIds,
        deletedApplications: selectResult.rows,
        deletedCount: deleteResult.rows.length,
        deletedBy: req.user.id,
        timestamp: new Date().toISOString()
    });

    res.json({
        status: 'success',
        message: `Successfully deleted ${deleteResult.rows.length} applications`,
        data: {
            deletedCount: deleteResult.rows.length,
            deletedApplicationIds: deleteResult.rows.map(row => row.id)
        }
    });
}));

/**
 * POST /api/admin/applications/bulk/export
 * Export selected applications to CSV
 */
router.post('/export', [
    body('applicationIds').isArray().withMessage('Application IDs must be an array'),
    body('applicationIds.*').isUUID().withMessage('Each application ID must be a valid UUID'),
    body('format').optional().isIn(['csv', 'json']).withMessage('Format must be csv or json')
], catchAsync(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: errors.array()
        });
    }

    const { applicationIds, format = 'csv' } = req.body;

    // Get applications data
    const placeholders = applicationIds.map((_, index) => `$${index + 1}`).join(',');
    const exportQuery = `
        SELECT
            a.id as application_id,
            a.status,
            a.loan_amount,
            a.loan_purpose,
            a.employment_status,
            a.annual_income,
            a.credit_score,
            a.created_at as application_created_at,
            a.submitted_at,
            a.reviewed_at,

            -- User information
            u.email as user_email,
            u.first_name,
            u.last_name,
            u.phone,
            u.role as user_role,
            u.email_verified,
            u.phone_verified,
            u.created_at as user_created_at

        FROM applications a
        JOIN users u ON a.user_id = u.id
        WHERE a.id IN (${placeholders})
        ORDER BY a.created_at DESC
    `;

    const result = await query(exportQuery, applicationIds);

    // Map statuses to frontend format
    const mappedApplications = result.rows.map(app => ({
        ...app,
        status: mapDbStatusToFrontend(app.status)
    }));

    if (format === 'csv') {
        // Generate CSV
        const csvHeaders = [
            'Application ID', 'Status', 'Loan Amount', 'Loan Purpose', 'Employment Status', 'Annual Income', 'Credit Score',
            'Application Created', 'Submitted At', 'Reviewed At',
            'User Email', 'First Name', 'Last Name', 'Phone', 'User Role', 'Email Verified', 'Phone Verified', 'User Created'
        ];

        let csvContent = csvHeaders.join(',') + '\n';

        mappedApplications.forEach(row => {
            const csvRow = [
                row.application_id, row.status, row.loan_amount, `"${row.loan_purpose || ''}"`,
                row.employment_status, row.annual_income, row.credit_score,
                row.application_created_at, row.submitted_at || '', row.reviewed_at || '',
                row.user_email, row.first_name, row.last_name, row.phone || '',
                row.user_role, row.email_verified, row.phone_verified, row.user_created_at
            ].map(val => val === null || val === undefined ? '' : val).join(',');

            csvContent += csvRow + '\n';
        });

        const timestamp = new Date().toISOString().split('T')[0];
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="loan_applications_${timestamp}.csv"`);
        res.send(csvContent);
    } else {
        // Return JSON
        res.json({
            status: 'success',
            data: {
                applications: mappedApplications,
                exportedAt: new Date().toISOString(),
                totalRecords: mappedApplications.length
            }
        });
    }

    logger.info('Bulk application export', {
        applicationIds,
        format,
        exportedCount: mappedApplications.length,
        exportedBy: req.user.id,
        timestamp: new Date().toISOString()
    });
}));

module.exports = router;
