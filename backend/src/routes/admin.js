const express = require('express');
const { body, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const { query } = require('../config/database');
const { authenticate, authorize, hashPassword } = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Helper function to map database statuses to frontend statuses
function mapDbStatusToFrontend(dbStatus) {
    const statusMapping = {
        'draft': 'new',
        'submitted': 'new',
        'under_review': 'under_review',
        'approved': 'approved',
        'rejected': 'declined',
        'declined': 'declined',
        'funded': 'approved'
    };
    return statusMapping[dbStatus] || dbStatus;
}

// Apply authentication and admin authorization to all routes
router.use(authenticate);
router.use(authorize('admin'));

/**
 * GET /api/admin/stats
 * Get comprehensive system statistics
 */
router.get('/stats', catchAsync(async (req, res) => {
    const [
        userStats,
        applicationStats,
        analyticsStats,
        recentActivity
    ] = await Promise.all([
        // User statistics
        query(`
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'applicant' THEN 1 END) as applicants,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admins,
                COUNT(CASE WHEN role = 'reviewer' THEN 1 END) as reviewers,
                COUNT(CASE WHEN email_verified = true THEN 1 END) as verified_users
            FROM users
        `),

        // Application statistics
        query(`
            SELECT
                COUNT(*) as total_applications,
                COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft,
                COUNT(CASE WHEN status = 'submitted' THEN 1 END) as submitted,
                COUNT(CASE WHEN status = 'under_review' THEN 1 END) as under_review,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
                COUNT(CASE WHEN status = 'funded' THEN 1 END) as funded,
                AVG(loan_amount) as avg_loan_amount,
                SUM(loan_amount) as total_loan_amount
            FROM applications
        `),

        // Analytics statistics
        query(`
            SELECT
                COUNT(*) as total_analytics_records,
                COUNT(DISTINCT user_id) as unique_users_tracked,
                COUNT(DISTINCT session_id) as unique_sessions,
                COUNT(DISTINCT ip_address) as unique_ips
            FROM user_analytics
        `),

        // Recent activity
        query(`
            SELECT
                'application' as type,
                a.id,
                a.status,
                a.loan_amount,
                a.created_at,
                u.email as user_email,
                u.first_name,
                u.last_name
            FROM applications a
            JOIN users u ON a.user_id = u.id
            ORDER BY a.created_at DESC
            LIMIT 10
        `)
    ]);

    res.json({
        status: 'success',
        data: {
            totalUsers: parseInt(userStats.rows[0].total_users),
            totalApplications: parseInt(applicationStats.rows[0].total_applications),
            usersByRole: {
                applicant: parseInt(userStats.rows[0].applicants),
                admin: parseInt(userStats.rows[0].admins),
                reviewer: parseInt(userStats.rows[0].reviewers)
            },
            applicationsByStatus: {
                draft: parseInt(applicationStats.rows[0].draft),
                submitted: parseInt(applicationStats.rows[0].submitted),
                under_review: parseInt(applicationStats.rows[0].under_review),
                approved: parseInt(applicationStats.rows[0].approved),
                rejected: parseInt(applicationStats.rows[0].rejected),
                funded: parseInt(applicationStats.rows[0].funded)
            },
            analytics: {
                totalRecords: parseInt(analyticsStats.rows[0].total_analytics_records),
                uniqueUsers: parseInt(analyticsStats.rows[0].unique_users_tracked),
                uniqueSessions: parseInt(analyticsStats.rows[0].unique_sessions),
                uniqueIps: parseInt(analyticsStats.rows[0].unique_ips)
            },
            financials: {
                averageLoanAmount: parseFloat(applicationStats.rows[0].avg_loan_amount) || 0,
                totalLoanAmount: parseFloat(applicationStats.rows[0].total_loan_amount) || 0
            },
            recentActivity: recentActivity.rows
        }
    });
}));

/**
 * GET /api/admin/applications/analytics
 * Get all applications with comprehensive analytics data
 * Supports filtering, sorting, and pagination
 */
router.get('/applications/analytics', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder || 'DESC';
    const dateFrom = req.query.dateFrom;
    const dateTo = req.query.dateTo;

    // Build WHERE clause
    let whereConditions = ['1=1'];
    const params = [];
    let paramCount = 0;

    if (search) {
        paramCount++;
        whereConditions.push(`(
            u.email ILIKE $${paramCount} OR
            u.first_name ILIKE $${paramCount} OR
            u.last_name ILIKE $${paramCount} OR
            a.loan_purpose ILIKE $${paramCount}
        )`);
        params.push(`%${search}%`);
    }

    if (status) {
        paramCount++;
        whereConditions.push(`a.status = $${paramCount}`);
        params.push(status);
    }

    if (dateFrom) {
        paramCount++;
        whereConditions.push(`a.created_at >= $${paramCount}`);
        params.push(dateFrom);
    }

    if (dateTo) {
        paramCount++;
        whereConditions.push(`a.created_at <= $${paramCount}`);
        params.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort column
    const validSortColumns = [
        'created_at', 'loan_amount', 'status', 'user_email',
        'city', 'country', 'device_type', 'browser_name'
    ];
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // Get total count
    const countQuery = `
        SELECT COUNT(DISTINCT a.id) as total
        FROM applications a
        JOIN users u ON a.user_id = u.id
        LEFT JOIN user_analytics ua ON a.id = ua.application_id
        WHERE ${whereClause}
    `;

    const countResult = await query(countQuery, params);
    const totalRecords = parseInt(countResult.rows[0].total);

    // Main query with all 48 analytics fields
    const mainQuery = `
        SELECT DISTINCT ON (a.id)
            a.id as application_id,
            a.status,
            a.loan_amount,
            a.loan_purpose,
            a.employment_status,
            a.annual_income,
            a.created_at as application_created_at,
            a.submitted_at,
            a.reviewed_at,

            -- User information
            u.id as user_id,
            u.email as user_email,
            u.first_name,
            u.last_name,
            u.phone,
            u.role as user_role,
            u.email_verified,
            u.created_at as user_created_at,

            -- Analytics data (all 48 fields)
            ua.id as analytics_id,
            ua.session_id,

            -- Network Information (6 fields)
            ua.ip_address,
            ua.country,
            ua.region,
            ua.city,
            ua.timezone,
            ua.isp,

            -- Browser Information (4 fields)
            ua.user_agent,
            ua.browser_name,
            ua.browser_version,
            ua.browser_engine,

            -- Operating System (3 fields)
            ua.os_name,
            ua.os_version,
            ua.platform,

            -- Device Information (6 fields)
            ua.device_type,
            ua.device_vendor,
            ua.device_model,
            ua.is_mobile,
            ua.is_tablet,
            ua.is_desktop,

            -- Screen Information (6 fields)
            ua.screen_width,
            ua.screen_height,
            ua.screen_color_depth,
            ua.screen_pixel_ratio,
            ua.viewport_width,
            ua.viewport_height,

            -- Browser Capabilities (5 fields)
            ua.languages,
            ua.timezone_offset,
            ua.cookies_enabled,
            ua.java_enabled,
            ua.flash_enabled,

            -- Hardware Information (4 fields)
            ua.cpu_cores,
            ua.memory_gb,
            ua.gpu_vendor,
            ua.gpu_renderer,

            -- Font Information (2 fields)
            ua.fonts_available,
            ua.fonts_count,

            -- Fingerprinting (3 fields)
            ua.canvas_fingerprint,
            ua.webgl_fingerprint,
            ua.audio_fingerprint,

            -- Behavioral Data (6 fields)
            ua.referrer,
            ua.utm_source,
            ua.utm_medium,
            ua.utm_campaign,
            ua.utm_term,
            ua.utm_content,

            ua.created_at as analytics_created_at

        FROM applications a
        JOIN users u ON a.user_id = u.id
        LEFT JOIN user_analytics ua ON a.id = ua.application_id
        WHERE ${whereClause}
        ORDER BY a.id, ua.created_at DESC
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    params.push(limit, offset);
    const result = await query(mainQuery, params);

    res.json({
        status: 'success',
        data: {
            applications: result.rows,
            pagination: {
                page,
                limit,
                total: totalRecords,
                totalPages: Math.ceil(totalRecords / limit),
                hasNext: page < Math.ceil(totalRecords / limit),
                hasPrev: page > 1
            }
        }
    });
}));

/**
 * GET /api/admin/applications/analytics/export
 * Export applications with analytics data to CSV
 */
router.get('/applications/analytics/export', catchAsync(async (req, res) => {
    const format = req.query.format || 'csv';
    const search = req.query.search || '';
    const status = req.query.status || '';
    const dateFrom = req.query.dateFrom;
    const dateTo = req.query.dateTo;

    // Build WHERE clause (same as main query)
    let whereConditions = ['1=1'];
    const params = [];
    let paramCount = 0;

    if (search) {
        paramCount++;
        whereConditions.push(`(
            u.email ILIKE $${paramCount} OR
            u.first_name ILIKE $${paramCount} OR
            u.last_name ILIKE $${paramCount} OR
            a.loan_purpose ILIKE $${paramCount}
        )`);
        params.push(`%${search}%`);
    }

    if (status) {
        paramCount++;
        whereConditions.push(`a.status = $${paramCount}`);
        params.push(status);
    }

    if (dateFrom) {
        paramCount++;
        whereConditions.push(`a.created_at >= $${paramCount}`);
        params.push(dateFrom);
    }

    if (dateTo) {
        paramCount++;
        whereConditions.push(`a.created_at <= $${paramCount}`);
        params.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    // Export query (no pagination)
    const exportQuery = `
        SELECT DISTINCT ON (a.id)
            a.id as application_id,
            a.status,
            a.loan_amount,
            a.loan_purpose,
            a.employment_status,
            a.annual_income,
            a.created_at as application_created_at,
            a.submitted_at,
            a.reviewed_at,

            -- User information
            u.email as user_email,
            u.first_name,
            u.last_name,
            u.phone,
            u.role as user_role,
            u.email_verified,
            u.created_at as user_created_at,

            -- Analytics data (all 48 fields)
            ua.session_id,
            ua.ip_address,
            ua.country,
            ua.region,
            ua.city,
            ua.timezone,
            ua.isp,
            ua.user_agent,
            ua.browser_name,
            ua.browser_version,
            ua.browser_engine,
            ua.os_name,
            ua.os_version,
            ua.platform,
            ua.device_type,
            ua.device_vendor,
            ua.device_model,
            ua.is_mobile,
            ua.is_tablet,
            ua.is_desktop,
            ua.screen_width,
            ua.screen_height,
            ua.screen_color_depth,
            ua.screen_pixel_ratio,
            ua.viewport_width,
            ua.viewport_height,
            ua.languages,
            ua.timezone_offset,
            ua.cookies_enabled,
            ua.java_enabled,
            ua.flash_enabled,
            ua.cpu_cores,
            ua.memory_gb,
            ua.gpu_vendor,
            ua.gpu_renderer,
            ua.fonts_available,
            ua.fonts_count,
            ua.canvas_fingerprint,
            ua.webgl_fingerprint,
            ua.audio_fingerprint,
            ua.referrer,
            ua.utm_source,
            ua.utm_medium,
            ua.utm_campaign,
            ua.utm_term,
            ua.utm_content,
            ua.created_at as analytics_created_at

        FROM applications a
        JOIN users u ON a.user_id = u.id
        LEFT JOIN user_analytics ua ON a.id = ua.application_id
        WHERE ${whereClause}
        ORDER BY a.id, ua.created_at DESC
    `;

    const result = await query(exportQuery, params);

    if (format === 'csv') {
        // Generate CSV
        const csvHeaders = [
            'Application ID', 'Status', 'Loan Amount', 'Loan Purpose', 'Employment Status', 'Annual Income',
            'Application Created', 'Submitted At', 'Reviewed At',
            'User Email', 'First Name', 'Last Name', 'Phone', 'User Role', 'Email Verified', 'User Created',
            'Session ID', 'IP Address', 'Country', 'Region', 'City', 'Timezone', 'ISP',
            'User Agent', 'Browser Name', 'Browser Version', 'Browser Engine',
            'OS Name', 'OS Version', 'Platform',
            'Device Type', 'Device Vendor', 'Device Model', 'Is Mobile', 'Is Tablet', 'Is Desktop',
            'Screen Width', 'Screen Height', 'Screen Color Depth', 'Screen Pixel Ratio', 'Viewport Width', 'Viewport Height',
            'Languages', 'Timezone Offset', 'Cookies Enabled', 'Java Enabled', 'Flash Enabled',
            'CPU Cores', 'Memory GB', 'GPU Vendor', 'GPU Renderer',
            'Fonts Available', 'Fonts Count',
            'Canvas Fingerprint', 'WebGL Fingerprint', 'Audio Fingerprint',
            'Referrer', 'UTM Source', 'UTM Medium', 'UTM Campaign', 'UTM Term', 'UTM Content',
            'Analytics Created'
        ];

        let csvContent = csvHeaders.join(',') + '\n';

        result.rows.forEach(row => {
            const csvRow = [
                row.application_id, row.status, row.loan_amount, `"${row.loan_purpose || ''}"`,
                row.employment_status, row.annual_income,
                row.application_created_at, row.submitted_at, row.reviewed_at,
                row.user_email, row.first_name, row.last_name, row.phone, row.user_role,
                row.email_verified, row.user_created_at,
                row.session_id, row.ip_address, row.country, row.region, row.city, row.timezone, row.isp,
                `"${row.user_agent || ''}"`, row.browser_name, row.browser_version, row.browser_engine,
                row.os_name, row.os_version, row.platform,
                row.device_type, row.device_vendor, row.device_model, row.is_mobile, row.is_tablet, row.is_desktop,
                row.screen_width, row.screen_height, row.screen_color_depth, row.screen_pixel_ratio,
                row.viewport_width, row.viewport_height,
                `"${Array.isArray(row.languages) ? row.languages.join(';') : ''}"`, row.timezone_offset,
                row.cookies_enabled, row.java_enabled, row.flash_enabled,
                row.cpu_cores, row.memory_gb, row.gpu_vendor, row.gpu_renderer,
                `"${Array.isArray(row.fonts_available) ? row.fonts_available.join(';') : ''}"`, row.fonts_count,
                row.canvas_fingerprint, row.webgl_fingerprint, row.audio_fingerprint,
                `"${row.referrer || ''}"`, row.utm_source, row.utm_medium, row.utm_campaign, row.utm_term, row.utm_content,
                row.analytics_created_at
            ].map(val => val === null || val === undefined ? '' : val).join(',');

            csvContent += csvRow + '\n';
        });

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="applications_analytics_${new Date().toISOString().split('T')[0]}.csv"`);
        res.send(csvContent);
    } else {
        // Return JSON for other formats
        res.json({
            status: 'success',
            data: {
                applications: result.rows,
                exportedAt: new Date().toISOString(),
                totalRecords: result.rows.length
            }
        });
    }
}));

/**
 * GET /api/admin/users
 * Get all users with filtering and pagination
 */
router.get('/users', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const role = req.query.role || '';
    const excludeRole = req.query.excludeRole || '';

    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    if (search) {
        paramCount++;
        whereClause += ` AND (first_name ILIKE $${paramCount} OR last_name ILIKE $${paramCount} OR email ILIKE $${paramCount})`;
        params.push(`%${search}%`);
    }

    if (role) {
        paramCount++;
        whereClause += ` AND role = $${paramCount}`;
        params.push(role);
    }

    if (excludeRole) {
        paramCount++;
        whereClause += ` AND role != $${paramCount}`;
        params.push(excludeRole);
    }

    // Get total count
    const countResult = await query(
        `SELECT COUNT(*) FROM users ${whereClause}`,
        params
    );

    // Get users
    const usersResult = await query(
        `SELECT id, email, first_name, last_name, phone, role, email_verified, phone_verified, created_at, last_login
         FROM users ${whereClause}
         ORDER BY created_at DESC
         LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}`,
        [...params, limit, offset]
    );

    const totalUsers = parseInt(countResult.rows[0].count);

    // Transform database field names to camelCase for frontend
    const transformedUsers = usersResult.rows.map(user => ({
        id: user.id,
        email: user.email,
        firstName: user.first_name,
        lastName: user.last_name,
        phone: user.phone,
        role: user.role,
        emailVerified: user.email_verified,
        phoneVerified: user.phone_verified,
        createdAt: user.created_at,
        lastLogin: user.last_login
    }));

    res.json({
        status: 'success',
        data: {
            users: transformedUsers,
            pagination: {
                page,
                limit,
                total: totalUsers,
                totalPages: Math.ceil(totalUsers / limit),
                hasNext: page < Math.ceil(totalUsers / limit),
                hasPrev: page > 1
            }
        }
    });
}));

/**
 * POST /api/admin/users
 * Create a new user (admin only)
 */
const validateUserCreation = [
    body('email')
        .isEmail()
        .withMessage('Valid email is required')
        .normalizeEmail(),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    body('firstName')
        .trim()
        .isLength({ min: 2 })
        .withMessage('First name must be at least 2 characters long'),
    body('lastName')
        .trim()
        .isLength({ min: 2 })
        .withMessage('Last name must be at least 2 characters long'),
    body('phone')
        .optional()
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Invalid phone number format'),
    body('role')
        .optional()
        .isIn(['applicant', 'reviewer', 'admin'])
        .withMessage('Role must be one of: applicant, reviewer, admin')
];

const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        return next(new AppError(errorMessages.join('. '), 400));
    }
    next();
};

router.post('/users', validateUserCreation, checkValidation, catchAsync(async (req, res, next) => {
    const { email, password, firstName, lastName, phone, role = 'applicant' } = req.body;

    // Check if user already exists
    const existingUser = await query(
        'SELECT id FROM users WHERE email = $1',
        [email]
    );

    if (existingUser.rows.length > 0) {
        return next(new AppError('User with this email already exists', 409));
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const result = await query(
        `INSERT INTO users (email, password_hash, first_name, last_name, phone, role, email_verified)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id, email, first_name, last_name, phone, role, email_verified, phone_verified, created_at`,
        [email, hashedPassword, firstName, lastName, phone, role, true]
    );

    const user = result.rows[0];

    logger.info('User created by admin', {
        userId: user.id,
        email: user.email,
        role: user.role,
        createdBy: req.user.id,
        ip: req.ip
    });

    res.status(201).json({
        status: 'success',
        message: 'User created successfully',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                phoneVerified: user.phone_verified,
                createdAt: user.created_at
            }
        }
    });
}));

/**
 * PATCH /api/admin/users/:id
 * Update user information (admin only)
 */
const validateUserUpdate = [
    body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2 })
        .withMessage('First name must be at least 2 characters long'),
    body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2 })
        .withMessage('Last name must be at least 2 characters long'),
    body('phone')
        .optional()
        .matches(/^\+?[\d\s\-\(\)]+$/)
        .withMessage('Invalid phone number format'),
    body('role')
        .optional()
        .isIn(['applicant', 'reviewer', 'admin'])
        .withMessage('Role must be one of: applicant, reviewer, admin'),
    body('emailVerified')
        .optional()
        .isBoolean()
        .withMessage('Email verified must be a boolean'),
    body('phoneVerified')
        .optional()
        .isBoolean()
        .withMessage('Phone verified must be a boolean')
];

router.patch('/users/:id', validateUserUpdate, checkValidation, catchAsync(async (req, res, next) => {
    const userId = req.params.id;
    const { firstName, lastName, phone, role, emailVerified, phoneVerified } = req.body;

    // Check if user exists
    const existingUser = await query(
        'SELECT id, email FROM users WHERE id = $1',
        [userId]
    );

    if (existingUser.rows.length === 0) {
        return next(new AppError('User not found', 404));
    }

    const updates = [];
    const params = [];
    let paramCount = 0;

    if (firstName !== undefined) {
        paramCount++;
        updates.push(`first_name = $${paramCount}`);
        params.push(firstName);
    }

    if (lastName !== undefined) {
        paramCount++;
        updates.push(`last_name = $${paramCount}`);
        params.push(lastName);
    }

    if (phone !== undefined) {
        paramCount++;
        updates.push(`phone = $${paramCount}`);
        params.push(phone);
    }

    if (role !== undefined) {
        paramCount++;
        updates.push(`role = $${paramCount}`);
        params.push(role);
    }

    if (emailVerified !== undefined) {
        paramCount++;
        updates.push(`email_verified = $${paramCount}`);
        params.push(emailVerified);
    }

    if (phoneVerified !== undefined) {
        paramCount++;
        updates.push(`phone_verified = $${paramCount}`);
        params.push(phoneVerified);
    }

    if (updates.length === 0) {
        return res.json({
            status: 'success',
            message: 'No changes to update'
        });
    }

    paramCount++;
    params.push(userId);

    const result = await query(
        `UPDATE users SET ${updates.join(', ')}, updated_at = NOW()
         WHERE id = $${paramCount}
         RETURNING id, email, first_name, last_name, phone, role, email_verified, phone_verified, updated_at`,
        params
    );

    const user = result.rows[0];

    logger.info('User updated by admin', {
        userId: user.id,
        email: user.email,
        updates: Object.keys(req.body),
        updatedBy: req.user.id,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'User updated successfully',
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone,
                role: user.role,
                emailVerified: user.email_verified,
                phoneVerified: user.phone_verified,
                updatedAt: user.updated_at
            }
        }
    });
}));

/**
 * DELETE /api/admin/users/:id
 * Delete user (admin only)
 */
router.delete('/users/:id', catchAsync(async (req, res, next) => {
    const userId = req.params.id;

    // Check if user exists
    const existingUser = await query(
        'SELECT id, email, role FROM users WHERE id = $1',
        [userId]
    );

    if (existingUser.rows.length === 0) {
        return next(new AppError('User not found', 404));
    }

    const user = existingUser.rows[0];

    // Prevent deleting the last admin
    if (user.role === 'admin') {
        const adminCount = await query(
            'SELECT COUNT(*) FROM users WHERE role = $1',
            ['admin']
        );

        if (parseInt(adminCount.rows[0].count) <= 1) {
            return next(new AppError('Cannot delete the last admin user', 400));
        }
    }

    // Delete user (this will cascade delete related records)
    await query('DELETE FROM users WHERE id = $1', [userId]);

    logger.info('User deleted by admin', {
        deletedUserId: userId,
        deletedUserEmail: user.email,
        deletedUserRole: user.role,
        deletedBy: req.user.id,
        ip: req.ip
    });

    res.json({
        status: 'success',
        message: 'User deleted successfully'
    });
}));

/**
 * GET /api/admin/applications
 * Get all applications with filtering and pagination for management
 */
router.get('/applications', catchAsync(async (req, res) => {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const sortBy = req.query.sortBy || 'created_at';
    const sortOrder = req.query.sortOrder || 'DESC';
    const dateFrom = req.query.dateFrom;
    const dateTo = req.query.dateTo;

    // Build WHERE clause
    let whereConditions = ['1=1'];
    const params = [];
    let paramCount = 0;

    if (search) {
        paramCount++;
        whereConditions.push(`(
            u.email ILIKE $${paramCount} OR
            u.first_name ILIKE $${paramCount} OR
            u.last_name ILIKE $${paramCount} OR
            a.loan_purpose ILIKE $${paramCount}
        )`);
        params.push(`%${search}%`);
    }

    if (status) {
        paramCount++;
        whereConditions.push(`a.status = $${paramCount}`);
        params.push(status);
    }

    if (dateFrom) {
        paramCount++;
        whereConditions.push(`a.created_at >= $${paramCount}`);
        params.push(dateFrom);
    }

    if (dateTo) {
        paramCount++;
        whereConditions.push(`a.created_at <= $${paramCount}`);
        params.push(dateTo);
    }

    const whereClause = whereConditions.join(' AND ');

    // Validate sort column
    const allowedSortColumns = ['created_at', 'loan_amount', 'status', 'submitted_at'];
    const sortColumn = allowedSortColumns.includes(sortBy) ? `a.${sortBy}` : 'a.created_at';
    const sortDirection = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // Get total count
    const countQuery = `
        SELECT COUNT(DISTINCT a.id)
        FROM applications a
        JOIN users u ON a.user_id = u.id
        WHERE ${whereClause}
    `;

    const countResult = await query(countQuery, params);
    const totalApplications = parseInt(countResult.rows[0].count);

    // Main query
    const mainQuery = `
        SELECT
            a.id as application_id,
            a.status,
            a.loan_amount,
            a.loan_purpose,
            a.employment_status,
            a.annual_income,

            a.created_at as application_created_at,
            a.submitted_at,
            a.reviewed_at,

            -- User information
            u.id as user_id,
            u.email as user_email,
            u.first_name,
            u.last_name,
            u.phone,
            u.role as user_role,
            u.email_verified,
            u.phone_verified,
            u.created_at as user_created_at

        FROM applications a
        JOIN users u ON a.user_id = u.id
        WHERE ${whereClause}
        ORDER BY ${sortColumn} ${sortDirection}
        LIMIT $${paramCount + 1} OFFSET $${paramCount + 2}
    `;

    const result = await query(mainQuery, [...params, limit, offset]);

    // Map database statuses to frontend statuses
    const mappedApplications = result.rows.map(app => ({
        ...app,
        status: mapDbStatusToFrontend(app.status)
    }));

    res.json({
        status: 'success',
        data: {
            applications: mappedApplications,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(totalApplications / limit),
                totalApplications,
                limit,
                hasNext: page < Math.ceil(totalApplications / limit),
                hasPrev: page > 1
            }
        }
    });
}));

/**
 * PUT /api/admin/applications/:id/status
 * Update application status
 */
router.put('/applications/:id/status', catchAsync(async (req, res) => {
    const { id } = req.params;
    const { status } = req.body;

    // Map frontend statuses to database statuses
    const statusMapping = {
        'new': 'submitted',
        'under_review': 'under_review',
        'approved': 'approved',
        'declined': 'declined'
    };

    const validStatuses = ['new', 'under_review', 'approved', 'declined'];
    if (!validStatuses.includes(status)) {
        return res.status(400).json({
            status: 'error',
            message: 'Invalid status. Must be one of: new, under_review, approved, declined'
        });
    }

    const dbStatus = statusMapping[status];

    // Update application status
    const updateQuery = `
        UPDATE applications
        SET status = $1,
            reviewed_at = CASE WHEN $1 IN ('under_review', 'approved', 'declined', 'rejected') THEN NOW() ELSE reviewed_at END
        WHERE id = $2
        RETURNING id, status, reviewed_at
    `;

    const result = await query(updateQuery, [dbStatus, id]);

    if (result.rows.length === 0) {
        return res.status(404).json({
            status: 'error',
            message: 'Application not found'
        });
    }

    logger.info('Application status updated', {
        applicationId: id,
        newStatus: status,
        updatedBy: req.user.id,
        timestamp: new Date().toISOString()
    });

    // Map the status back to frontend format
    const updatedApp = {
        ...result.rows[0],
        status: mapDbStatusToFrontend(result.rows[0].status)
    };

    res.json({
        status: 'success',
        data: {
            application: updatedApp
        }
    });
}));

/**
 * GET /api/admin/applications/:id
 * Get detailed application information
 */
router.get('/applications/:id', catchAsync(async (req, res) => {
    const { id } = req.params;

    const applicationQuery = `
        SELECT
            a.*,
            u.email as user_email,
            u.first_name,
            u.last_name,
            u.phone,
            u.email_verified,
            u.phone_verified,
            u.created_at as user_created_at
        FROM applications a
        JOIN users u ON a.user_id = u.id
        WHERE a.id = $1
    `;

    const result = await query(applicationQuery, [id]);

    if (result.rows.length === 0) {
        return res.status(404).json({
            status: 'error',
            message: 'Application not found'
        });
    }

    // Map the status to frontend format
    const mappedApp = {
        ...result.rows[0],
        status: mapDbStatusToFrontend(result.rows[0].status)
    };

    res.json({
        status: 'success',
        data: {
            application: mappedApp
        }
    });
}));

module.exports = router;
