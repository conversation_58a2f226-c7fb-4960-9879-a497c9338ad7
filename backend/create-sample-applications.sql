-- Create realistic sample loan applications for all applicant users
-- This script populates the applications table with diverse, realistic data

-- Sample applications with varied statuses, amounts, and purposes
INSERT INTO applications (
    user_id,
    status,
    loan_amount,
    loan_purpose,
    employment_status,
    annual_income,
    credit_score,
    personal_info,
    address_info,
    financial_info,
    form_data,
    ip_address,
    user_agent,
    referrer,
    created_at,
    submitted_at,
    reviewed_at
) VALUES

-- 1. <PERSON> - Home Improvement (Approved)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'approved',
    25000.00,
    'Home improvement',
    'Full-time employed',
    78000.00,
    745,
    '{"firstName": "<PERSON>", "lastName": "<PERSON>", "dateOfBirth": "1988-07-12", "ssn": "***-**-4567", "maritalStatus": "Married", "dependents": 2}',
    '{"street": "456 Oak Avenue", "city": "Austin", "state": "TX", "zipCode": "73301", "residenceType": "Own", "monthsAtAddress": 48, "monthlyRent": 0}',
    '{"bankName": "Wells Fargo", "accountType": "Checking", "monthlyIncome": 6500.00, "monthlyExpenses": 4200.00, "existingDebts": 8500.00, "savingsBalance": 15000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 60, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'https://google.com/search?q=home+improvement+loans',
    NOW() - INTERVAL '12 days',
    NOW() - INTERVAL '12 days' + INTERVAL '3 hours',
    NOW() - INTERVAL '8 days'
),

-- 2. Robert Martinez - Business Expansion (Under Review)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'under_review',
    45000.00,
    'Business expansion',
    'Self-employed',
    95000.00,
    720,
    '{"firstName": "Robert", "lastName": "Martinez", "dateOfBirth": "1982-03-25", "ssn": "***-**-7890", "maritalStatus": "Single", "dependents": 0}',
    '{"street": "789 Business Blvd", "city": "Miami", "state": "FL", "zipCode": "33101", "residenceType": "Rent", "monthsAtAddress": 24, "monthlyRent": 2800.00}',
    '{"bankName": "Bank of America", "accountType": "Business Checking", "monthlyIncome": 7916.67, "monthlyExpenses": 5200.00, "existingDebts": 12000.00, "savingsBalance": 25000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": false, "loanTerm": 84, "preferredContactMethod": "phone"}',
    '*************',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'https://bing.com/search?q=business+loans+small+business',
    NOW() - INTERVAL '5 days',
    NOW() - INTERVAL '5 days' + INTERVAL '1 hour',
    NOW() - INTERVAL '3 days'
),

-- 3. Lisa Garcia - Debt Consolidation (Submitted)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'submitted',
    18500.00,
    'Debt consolidation',
    'Full-time employed',
    62000.00,
    680,
    '{"firstName": "Lisa", "lastName": "Garcia", "dateOfBirth": "1990-11-08", "ssn": "***-**-2345", "maritalStatus": "Divorced", "dependents": 1}',
    '{"street": "321 Pine Street", "city": "Denver", "state": "CO", "zipCode": "80201", "residenceType": "Rent", "monthsAtAddress": 18, "monthlyRent": 1850.00}',
    '{"bankName": "Chase Bank", "accountType": "Checking", "monthlyIncome": 5166.67, "monthlyExpenses": 4100.00, "existingDebts": 22000.00, "savingsBalance": 3500.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 72, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/******** Firefox/120.0',
    'https://yahoo.com/search?p=debt+consolidation+loans',
    NOW() - INTERVAL '3 days',
    NOW() - INTERVAL '3 days' + INTERVAL '2 hours',
    NULL
),

-- 4. David Wilson - Medical Expenses (Approved)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'approved',
    12000.00,
    'Medical expenses',
    'Full-time employed',
    55000.00,
    710,
    '{"firstName": "David", "lastName": "Wilson", "dateOfBirth": "1985-09-14", "ssn": "***-**-6789", "maritalStatus": "Married", "dependents": 3}',
    '{"street": "654 Maple Drive", "city": "Phoenix", "state": "AZ", "zipCode": "85001", "residenceType": "Own", "monthsAtAddress": 72, "monthlyRent": 0}',
    '{"bankName": "US Bank", "accountType": "Checking", "monthlyIncome": 4583.33, "monthlyExpenses": 3800.00, "existingDebts": 5500.00, "savingsBalance": 8000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": false, "loanTerm": 48, "preferredContactMethod": "phone"}',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'https://google.com/search?q=medical+emergency+loans',
    NOW() - INTERVAL '18 days',
    NOW() - INTERVAL '18 days' + INTERVAL '4 hours',
    NOW() - INTERVAL '15 days'
),

-- 5. Emily Davis - Education (New/Recently Submitted)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'submitted',
    8500.00,
    'Education',
    'Part-time employed',
    28000.00,
    650,
    '{"firstName": "Emily", "lastName": "Davis", "dateOfBirth": "1995-05-20", "ssn": "***-**-3456", "maritalStatus": "Single", "dependents": 0}',
    '{"street": "987 College Ave", "city": "Boston", "state": "MA", "zipCode": "02101", "residenceType": "Rent", "monthsAtAddress": 12, "monthlyRent": 1200.00}',
    '{"bankName": "TD Bank", "accountType": "Student Checking", "monthlyIncome": 2333.33, "monthlyExpenses": 2000.00, "existingDebts": 15000.00, "savingsBalance": 1200.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 60, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    'https://duckduckgo.com/?q=student+loans+education+financing',
    NOW() - INTERVAL '1 day',
    NOW() - INTERVAL '1 day' + INTERVAL '6 hours',
    NULL
),

-- 6. Michael Brown - Car Purchase (Rejected)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'rejected',
    22000.00,
    'Vehicle purchase',
    'Full-time employed',
    48000.00,
    580,
    '{"firstName": "Michael", "lastName": "Brown", "dateOfBirth": "1987-12-03", "ssn": "***-**-4567", "maritalStatus": "Single", "dependents": 0}',
    '{"street": "147 Elm Street", "city": "Seattle", "state": "WA", "zipCode": "98101", "residenceType": "Rent", "monthsAtAddress": 36, "monthlyRent": 1950.00}',
    '{"bankName": "KeyBank", "accountType": "Checking", "monthlyIncome": 4000.00, "monthlyExpenses": 3600.00, "existingDebts": 28000.00, "savingsBalance": 800.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": false, "loanTerm": 72, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'https://google.com/search?q=auto+loans+car+financing',
    NOW() - INTERVAL '25 days',
    NOW() - INTERVAL '25 days' + INTERVAL '1 hour',
    NOW() - INTERVAL '20 days'
),

-- 7. Sarah Johnson - Wedding (Under Review)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'under_review',
    15000.00,
    'Wedding expenses',
    'Full-time employed',
    68000.00,
    725,
    '{"firstName": "Sarah", "lastName": "Johnson", "dateOfBirth": "1989-04-17", "ssn": "***-**-5678", "maritalStatus": "Engaged", "dependents": 0}',
    '{"street": "258 Rose Lane", "city": "Nashville", "state": "TN", "zipCode": "37201", "residenceType": "Rent", "monthsAtAddress": 30, "monthlyRent": 1650.00}',
    '{"bankName": "Regions Bank", "accountType": "Checking", "monthlyIncome": 5666.67, "monthlyExpenses": 3200.00, "existingDebts": 9500.00, "savingsBalance": 12000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 48, "preferredContactMethod": "phone"}',
    '*************',
    'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'https://pinterest.com/wedding-planning',
    NOW() - INTERVAL '7 days',
    NOW() - INTERVAL '7 days' + INTERVAL '5 hours',
    NOW() - INTERVAL '4 days'
),

-- 8. John Smith - Home Purchase Down Payment (Approved)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'approved',
    35000.00,
    'Home purchase down payment',
    'Full-time employed',
    85000.00,
    760,
    '{"firstName": "John", "lastName": "Smith", "dateOfBirth": "1983-01-30", "ssn": "***-**-6789", "maritalStatus": "Married", "dependents": 2}',
    '{"street": "369 Valley Road", "city": "San Diego", "state": "CA", "zipCode": "92101", "residenceType": "Rent", "monthsAtAddress": 60, "monthlyRent": 2400.00}',
    '{"bankName": "Navy Federal", "accountType": "Checking", "monthlyIncome": 7083.33, "monthlyExpenses": 4800.00, "existingDebts": 11000.00, "savingsBalance": 45000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": false, "loanTerm": 84, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    'https://zillow.com/homes-for-sale',
    NOW() - INTERVAL '21 days',
    NOW() - INTERVAL '21 days' + INTERVAL '2 hours',
    NOW() - INTERVAL '16 days'
),

-- 9. Jane Doe - Emergency Fund (Submitted)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'submitted',
    5000.00,
    'Emergency expenses',
    'Full-time employed',
    42000.00,
    695,
    '{"firstName": "Jane", "lastName": "Doe", "dateOfBirth": "1992-08-22", "ssn": "***-**-7890", "maritalStatus": "Single", "dependents": 0}',
    '{"street": "741 First Avenue", "city": "Portland", "state": "OR", "zipCode": "97201", "residenceType": "Rent", "monthsAtAddress": 15, "monthlyRent": 1400.00}',
    '{"bankName": "Columbia Bank", "accountType": "Checking", "monthlyIncome": 3500.00, "monthlyExpenses": 2900.00, "existingDebts": 7200.00, "savingsBalance": 2100.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 36, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Android 14; Mobile; rv:120.0) Gecko/120.0 Firefox/120.0',
    'https://google.com/search?q=emergency+personal+loans',
    NOW() - INTERVAL '2 days',
    NOW() - INTERVAL '2 days' + INTERVAL '3 hours',
    NULL
);

-- Add some additional applications for variety (multiple applications per user)
INSERT INTO applications (
    user_id,
    status,
    loan_amount,
    loan_purpose,
    employment_status,
    annual_income,
    credit_score,
    personal_info,
    address_info,
    financial_info,
    form_data,
    ip_address,
    user_agent,
    referrer,
    created_at,
    submitted_at,
    reviewed_at
) VALUES

-- 10. Jennifer Anderson - Second Application (Vacation - Rejected)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'rejected',
    8000.00,
    'Vacation',
    'Full-time employed',
    78000.00,
    745,
    '{"firstName": "Jennifer", "lastName": "Anderson", "dateOfBirth": "1988-07-12", "ssn": "***-**-4567", "maritalStatus": "Married", "dependents": 2}',
    '{"street": "456 Oak Avenue", "city": "Austin", "state": "TX", "zipCode": "73301", "residenceType": "Own", "monthsAtAddress": 48, "monthlyRent": 0}',
    '{"bankName": "Wells Fargo", "accountType": "Checking", "monthlyIncome": 6500.00, "monthlyExpenses": 4200.00, "existingDebts": 33500.00, "savingsBalance": 8000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": true, "loanTerm": 36, "preferredContactMethod": "email"}',
    '*************',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'https://expedia.com/vacation-packages',
    NOW() - INTERVAL '4 days',
    NOW() - INTERVAL '4 days' + INTERVAL '1 hour',
    NOW() - INTERVAL '2 days'
),

-- 11. David Wilson - Second Application (Pool Installation - New)
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'submitted',
    28000.00,
    'Pool installation',
    'Full-time employed',
    55000.00,
    710,
    '{"firstName": "David", "lastName": "Wilson", "dateOfBirth": "1985-09-14", "ssn": "***-**-6789", "maritalStatus": "Married", "dependents": 3}',
    '{"street": "654 Maple Drive", "city": "Phoenix", "state": "AZ", "zipCode": "85001", "residenceType": "Own", "monthsAtAddress": 72, "monthlyRent": 0}',
    '{"bankName": "US Bank", "accountType": "Checking", "monthlyIncome": 4583.33, "monthlyExpenses": 3800.00, "existingDebts": 5500.00, "savingsBalance": 15000.00}',
    '{"agreeToTerms": true, "agreeToCredit": true, "marketingConsent": false, "loanTerm": 72, "preferredContactMethod": "phone"}',
    '*************',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'https://google.com/search?q=pool+installation+financing',
    NOW() - INTERVAL '6 hours',
    NOW() - INTERVAL '6 hours' + INTERVAL '30 minutes',
    NULL
);
