const fs = require('fs');
const path = require('path');
const { connectDB, query } = require('./src/config/database');

async function createSampleApplications() {
    // Initialize database connection
    await connectDB();
    try {
        console.log('Reading SQL file...');
        const sqlFile = path.join(__dirname, 'create-sample-applications.sql');
        const sql = fs.readFileSync(sqlFile, 'utf8');

        console.log('Executing SQL to create sample applications...');
        await query(sql);

        console.log('✅ Sample applications created successfully!');

        // Verify the applications were created
        const result = await query('SELECT COUNT(*) as count FROM applications');
        console.log(`📊 Total applications in database: ${result.rows[0].count}`);

        // Show breakdown by status
        const statusBreakdown = await query(`
            SELECT status, COUNT(*) as count
            FROM applications
            GROUP BY status
            ORDER BY count DESC
        `);

        console.log('\n📈 Applications by status:');
        statusBreakdown.rows.forEach(row => {
            console.log(`  ${row.status}: ${row.count}`);
        });

        process.exit(0);
    } catch (error) {
        console.error('❌ Error creating sample applications:', error);
        process.exit(1);
    }
}

createSampleApplications();
