import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useForm } from 'react-hook-form'
import { useLocation, useNavigate } from 'react-router-dom'
import toast from 'react-hot-toast'
import {
  Users,
  UserPlus,
  FileText,
  BarChart3,
  Search,
  Edit,
  Trash2,
  Shield,
  User,
  Eye,
  EyeOff,
  X,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { adminApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import ApplicationAnalyticsTable from '@/components/ApplicationAnalyticsTable'
import ApplicantManagementTable from '@/components/ApplicantManagementTable'
import type { User as UserType, RegisterData } from '@/types'

interface CreateUserFormData extends RegisterData {
  role: 'applicant' | 'reviewer' | 'admin'
  confirmPassword: string
}

interface EditUserFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  role: 'applicant' | 'reviewer' | 'admin'
  password?: string
  confirmPassword?: string
}

function EditUserFormContent({ user, onClose, updateUserMutation }: {
  user: UserType
  onClose: () => void
  updateUserMutation: any
}) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<EditUserFormData>({
    defaultValues: {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone || '',
      role: user.role as 'applicant' | 'reviewer' | 'admin',
      password: '',
      confirmPassword: ''
    }
  })

  const password = watch('password')

  const onSubmit = async (data: EditUserFormData) => {
    try {
      // Prepare data for submission
      const submitData: any = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        role: data.role
      }

      // Only include password if it's provided
      if (data.password && data.password.trim() !== '') {
        submitData.password = data.password
      }

      await updateUserMutation.mutateAsync({ id: user.id, data: submitData })

      // Clear password fields for security
      reset({
        ...data,
        password: '',
        confirmPassword: ''
      })

      onClose()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="label">First Name</label>
          <input
            {...register('firstName', {
              required: 'First name is required',
              minLength: { value: 2, message: 'Minimum 2 characters' }
            })}
            type="text"
            className={`input ${errors.firstName ? 'input-error' : ''}`}
            placeholder="John"
          />
          {errors.firstName && (
            <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
          )}
        </div>

        <div>
          <label className="label">Last Name</label>
          <input
            {...register('lastName', {
              required: 'Last name is required',
              minLength: { value: 2, message: 'Minimum 2 characters' }
            })}
            type="text"
            className={`input ${errors.lastName ? 'input-error' : ''}`}
            placeholder="Doe"
          />
          {errors.lastName && (
            <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="label">Email</label>
        <input
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Invalid email address'
            }
          })}
          type="email"
          className={`input ${errors.email ? 'input-error' : ''}`}
          placeholder="<EMAIL>"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label className="label">Phone (Optional)</label>
        <input
          {...register('phone', {
            pattern: {
              value: /^\+?[\d\s\-\(\)]+$/,
              message: 'Invalid phone number'
            }
          })}
          type="tel"
          className={`input ${errors.phone ? 'input-error' : ''}`}
          placeholder="+****************"
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
        )}
      </div>

      <div>
        <label className="label">Role</label>
        <select
          {...register('role', { required: 'Role is required' })}
          className={`input ${errors.role ? 'input-error' : ''}`}
        >
          <option value="reviewer">Reviewer</option>
          <option value="admin">Admin</option>
        </select>
        {errors.role && (
          <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
        )}
      </div>

      {/* Password Change Section */}
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Change Password (Optional)</h4>

        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="label">New Password</label>
            <div className="relative">
              <input
                {...register('password', {
                  validate: (value) => {
                    if (!value || value.trim() === '') return true // Optional field
                    if (value.length < 8) return 'Password must be at least 8 characters long'
                    if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter'
                    if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter'
                    if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number'
                    return true
                  }
                })}
                type={showPassword ? 'text' : 'password'}
                className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
                placeholder="Enter new password (leave blank to keep current)"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {errors.password && (
              <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
            )}
          </div>

          <div>
            <label className="label">Confirm New Password</label>
            <div className="relative">
              <input
                {...register('confirmPassword', {
                  validate: (value) => {
                    if (!password || password.trim() === '') return true // Only validate if password is provided
                    if (!value || value.trim() === '') return 'Please confirm your password'
                    if (value !== password) return 'Passwords do not match'
                    return true
                  }
                })}
                type={showConfirmPassword ? 'text' : 'password'}
                className={`input pr-10 ${errors.confirmPassword ? 'input-error' : ''}`}
                placeholder="Confirm new password"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
            )}
          </div>
        </div>

        <div className="mt-2 text-xs text-gray-500">
          <p>Password requirements:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>At least 8 characters long</li>
            <li>Contains at least one lowercase letter</li>
            <li>Contains at least one uppercase letter</li>
            <li>Contains at least one number</li>
          </ul>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary w-full sm:w-auto order-2 sm:order-1"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting || updateUserMutation.isLoading}
          className="btn btn-primary w-full sm:w-auto order-1 sm:order-2"
        >
          {isSubmitting || updateUserMutation.isLoading ? (
            <div className="flex items-center justify-center">
              <LoadingSpinner size="sm" className="mr-2" />
              Updating...
            </div>
          ) : (
            'Update User'
          )}
        </button>
      </div>
    </form>
  )
}

export default function AdminDashboard() {
  const location = useLocation()
  const navigate = useNavigate()
  const [showCreateUserModal, setShowCreateUserModal] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [selectedUser, setSelectedUser] = useState<UserType | null>(null)
  const [userFilters, setUserFilters] = useState({
    search: '',
    role: '',
    page: 1,
    limit: 10
  })

  const queryClient = useQueryClient()

  // Determine active tab from URL
  const getActiveTab = () => {
    const path = location.pathname
    if (path === '/admin' || path === '/admin/overview') return 'overview'
    if (path === '/admin/users') return 'users'
    if (path === '/admin/applications') return 'applications'
    if (path === '/admin/application-analytics') return 'application-analytics'
    if (path === '/admin/system-analytics') return 'analytics'
    return 'overview'
  }

  const activeTab = getActiveTab()

  // Redirect /admin to /admin/overview
  useEffect(() => {
    if (location.pathname === '/admin') {
      navigate('/admin/overview', { replace: true })
    }
  }, [location.pathname, navigate])

  // Queries
  const { data: systemStats, isLoading: statsLoading } = useQuery(
    'admin-stats',
    adminApi.getSystemStats,
    { enabled: activeTab === 'overview' }
  )

  const { data: usersData, isLoading: usersLoading } = useQuery(
    ['admin-users', userFilters],
    () => {
      const queryParams = { ...userFilters };
      // Only fetch admin and reviewer users for User Management section
      // If no specific role is selected, fetch both admin and reviewer
      if (!userFilters.role) {
        queryParams.role = 'admin,reviewer';
      }
      return adminApi.getUsers(queryParams);
    },
    { enabled: activeTab === 'users' }
  )

  // Mutations
  const createUserMutation = useMutation(adminApi.createUser, {
    onSuccess: () => {
      toast.success('User created successfully')
      setShowCreateUserModal(false)
      queryClient.invalidateQueries('admin-users')
      queryClient.invalidateQueries('admin-stats')
      reset()
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create user')
    }
  })

  const updateUserMutation = useMutation(
    ({ id, data }: { id: string; data: Partial<UserType> }) => adminApi.updateUser(id, data),
    {
      onSuccess: () => {
        toast.success('User updated successfully')
        queryClient.invalidateQueries('admin-users')
        setSelectedUser(null)
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to update user')
      }
    }
  )

  const deleteUserMutation = useMutation(adminApi.deleteUser, {
    onSuccess: () => {
      toast.success('User deleted successfully')
      queryClient.invalidateQueries('admin-users')
      queryClient.invalidateQueries('admin-stats')
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete user')
    }
  })

  // Form handling
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<CreateUserFormData>()

  const password = watch('password')

  const onSubmit = async (data: CreateUserFormData) => {
    const { confirmPassword, ...userData } = data
    await createUserMutation.mutateAsync(userData)
  }

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      await deleteUserMutation.mutateAsync(userId)
    }
  }

  const handleUpdateUserRole = async (userId: string, role: string) => {
    await updateUserMutation.mutateAsync({ id: userId, data: { role } as any })
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3, path: '/admin/overview' },
    { id: 'users', label: 'User Management', icon: Users, path: '/admin/users' },
    { id: 'applications', label: 'Applications', icon: FileText, path: '/admin/applications' },
    { id: 'application-analytics', label: 'Application Analytics', icon: BarChart3, path: '/admin/application-analytics' },
    { id: 'analytics', label: 'System Analytics', icon: BarChart3, path: '/admin/system-analytics' }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-sm sm:text-base text-gray-600">Manage users, applications, and system settings</p>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-6 sm:mb-8">
          {/* Mobile Tab Selector */}
          <div className="sm:hidden mb-4">
            <select
              value={activeTab}
              onChange={(e) => {
                const selectedTab = tabs.find(tab => tab.id === e.target.value)
                if (selectedTab) {
                  navigate(selectedTab.path)
                }
              }}
              className="w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-primary-500 focus:outline-none focus:ring-primary-500"
            >
              {tabs.map((tab) => (
                <option key={tab.id} value={tab.id}>
                  {tab.label}
                </option>
              ))}
            </select>
          </div>

          {/* Desktop Tab Navigation */}
          <nav className="hidden sm:flex -mb-px space-x-4 lg:space-x-8 overflow-x-auto" role="tablist">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => navigate(tab.path)}
                  className={`whitespace-nowrap flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 ease-in-out rounded-t-md focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 bg-primary-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                  role="tab"
                  aria-selected={activeTab === tab.id}
                  aria-controls={`${tab.id}-panel`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden lg:inline">{tab.label}</span>
                  <span className="lg:hidden">{tab.label.split(' ')[0]}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {statsLoading ? (
              <div className="flex justify-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <>
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Users className="h-8 w-8 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Total Users</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {systemStats?.totalUsers || 0}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <FileText className="h-8 w-8 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Applications</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {systemStats?.totalApplications || 0}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Shield className="h-8 w-8 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Admins</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {systemStats?.usersByRole?.admin || 0}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Users className="h-8 w-8 text-orange-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-500">Reviewers</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {systemStats?.usersByRole?.reviewer || 0}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                      onClick={() => {
                        navigate('/admin/users')
                        setShowCreateUserModal(true)
                      }}
                      className="btn btn-outline-primary flex items-center space-x-3 p-4 h-auto text-left"
                    >
                      <UserPlus className="w-6 h-6 flex-shrink-0" />
                      <div className="text-left">
                        <p className="font-medium">Create Admin/Reviewer</p>
                        <p className="text-sm opacity-75">Add admin or reviewer account</p>
                      </div>
                    </button>

                    <button
                      onClick={() => navigate('/admin/users')}
                      className="btn btn-outline-secondary flex items-center space-x-3 p-4 h-auto text-left"
                    >
                      <Users className="w-6 h-6 flex-shrink-0" />
                      <div className="text-left">
                        <p className="font-medium">Manage Staff</p>
                        <p className="text-sm opacity-75">View and edit admin/reviewer accounts</p>
                      </div>
                    </button>

                    <button
                      onClick={() => navigate('/admin/applications')}
                      className="btn btn-outline-info flex items-center space-x-3 p-4 h-auto text-left"
                    >
                      <FileText className="w-6 h-6 flex-shrink-0" />
                      <div className="text-left">
                        <p className="font-medium">Manage Applications</p>
                        <p className="text-sm opacity-75">Review applicants and loan applications</p>
                      </div>
                    </button>
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Users Management Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            {/* Users Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">User Management</h2>
                <p className="text-gray-600">Manage admin and reviewer accounts</p>
              </div>
              <button
                onClick={() => setShowCreateUserModal(true)}
                className="mt-4 sm:mt-0 btn btn-primary"
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Create User
              </button>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search users..."
                      value={userFilters.search}
                      onChange={(e) => setUserFilters(prev => ({ ...prev, search: e.target.value, page: 1 }))}
                      className="input pl-10"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                  <select
                    value={userFilters.role}
                    onChange={(e) => setUserFilters(prev => ({ ...prev, role: e.target.value, page: 1 }))}
                    className="input"
                  >
                    <option value="">All Roles</option>
                    <option value="admin">Admin</option>
                    <option value="reviewer">Reviewer</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Per Page</label>
                  <select
                    value={userFilters.limit}
                    onChange={(e) => setUserFilters(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
                    className="input"
                  >
                    <option value={10}>10</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              {usersLoading ? (
                <div className="flex justify-center py-12">
                  <LoadingSpinner size="lg" />
                </div>
              ) : usersData?.users?.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                  <p className="text-gray-600 mb-4">Get started by creating your first user account.</p>
                  <button
                    onClick={() => setShowCreateUserModal(true)}
                    className="btn btn-primary"
                  >
                    <UserPlus className="w-4 h-4 mr-2" />
                    Create User
                  </button>
                </div>
              ) : (
                <>
                  {/* Desktop Table View */}
                  <div className="hidden lg:block overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Role
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Created
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Login
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {usersData?.users?.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900">
                                  {user.firstName} {user.lastName}
                                </div>
                                <div className="text-sm text-gray-500">{user.email}</div>
                                {user.phone && (
                                  <div className="text-sm text-gray-500">{user.phone}</div>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <select
                                value={user.role}
                                onChange={(e) => handleUpdateUserRole(user.id, e.target.value)}
                                className="text-sm border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                              >
                                <option value="reviewer">Reviewer</option>
                                <option value="admin">Admin</option>
                              </select>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex flex-col space-y-1">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  user.emailVerified
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {user.emailVerified ? 'Email Verified' : 'Email Pending'}
                                </span>
                                {user.phone && (
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    user.phoneVerified
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {user.phoneVerified ? 'Phone Verified' : 'Phone Pending'}
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(user.createdAt).toLocaleDateString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex items-center justify-end space-x-2">
                                <button
                                  onClick={() => setSelectedUser(user)}
                                  className="btn btn-sm btn-outline-primary"
                                  title="Edit user"
                                  aria-label="Edit user"
                                >
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDeleteUser(user.id)}
                                  className="btn btn-sm btn-outline-danger"
                                  title="Delete user"
                                  aria-label="Delete user"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Card View */}
                  <div className="lg:hidden">
                    <div className="divide-y divide-gray-200">
                      {usersData?.users?.map((user) => (
                        <div key={user.id} className="p-4 hover:bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-3 mb-2">
                                <div className="flex-shrink-0">
                                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <User className="w-5 h-5 text-primary-600" />
                                  </div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm font-medium text-gray-900 truncate">
                                    {user.firstName} {user.lastName}
                                  </p>
                                  <p className="text-sm text-gray-500 truncate">{user.email}</p>
                                  {user.phone && (
                                    <p className="text-sm text-gray-500 truncate">{user.phone}</p>
                                  )}
                                </div>
                              </div>

                              <div className="grid grid-cols-2 gap-3 text-sm">
                                <div>
                                  <span className="text-gray-500">Role:</span>
                                  <select
                                    value={user.role}
                                    onChange={(e) => handleUpdateUserRole(user.id, e.target.value)}
                                    className="ml-1 text-sm border-gray-300 rounded focus:ring-primary-500 focus:border-primary-500"
                                  >
                                    <option value="reviewer">Reviewer</option>
                                    <option value="admin">Admin</option>
                                  </select>
                                </div>
                                <div>
                                  <span className="text-gray-500">Created:</span>
                                  <span className="ml-1 text-gray-900">
                                    {new Date(user.createdAt).toLocaleDateString()}
                                  </span>
                                </div>
                              </div>

                              <div className="mt-2 flex flex-wrap gap-1">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  user.emailVerified
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {user.emailVerified ? 'Email ✓' : 'Email Pending'}
                                </span>
                                {user.phone && (
                                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                    user.phoneVerified
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  }`}>
                                    {user.phoneVerified ? 'Phone ✓' : 'Phone Pending'}
                                  </span>
                                )}
                              </div>
                            </div>

                            <div className="flex items-center space-x-2 ml-4">
                              <button
                                onClick={() => setSelectedUser(user)}
                                className="btn btn-sm btn-outline-primary btn-touch"
                                title="Edit user"
                                aria-label="Edit user"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteUser(user.id)}
                                className="btn btn-sm btn-outline-danger btn-touch"
                                title="Delete user"
                                aria-label="Delete user"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Pagination */}
                  {usersData?.pagination && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                      <div className="flex-1 flex justify-between sm:hidden">
                        <button
                          onClick={() => setUserFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                          disabled={!usersData.pagination.hasPrev}
                          className="btn btn-outline-secondary"
                        >
                          Previous
                        </button>
                        <button
                          onClick={() => setUserFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                          disabled={!usersData.pagination.hasNext}
                          className="btn btn-outline-secondary"
                        >
                          Next
                        </button>
                      </div>
                      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                          <p className="text-sm text-gray-700">
                            Showing{' '}
                            <span className="font-medium">
                              {((usersData.pagination.page - 1) * userFilters.limit) + 1}
                            </span>{' '}
                            to{' '}
                            <span className="font-medium">
                              {Math.min(
                                usersData.pagination.page * userFilters.limit,
                                usersData.pagination.total || 0
                              )}
                            </span>{' '}
                            of{' '}
                            <span className="font-medium">{usersData.pagination.total || 0}</span>{' '}
                            results
                          </p>
                        </div>
                        <div>
                          <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <div className="btn-group">
                              <button
                                onClick={() => setUserFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                                disabled={!usersData.pagination.hasPrev}
                                className="btn btn-outline-secondary"
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </button>
                              <span className="btn btn-outline-light cursor-default">
                                Page {usersData.pagination.page} of {usersData.pagination.totalPages}
                              </span>
                              <button
                                onClick={() => setUserFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                                disabled={!usersData.pagination.hasNext}
                                className="btn btn-outline-secondary"
                              >
                                <ChevronRight className="h-4 w-4" />
                              </button>
                            </div>
                          </nav>
                        </div>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <ApplicantManagementTable />
        )}

        {/* Application Analytics Tab */}
        {activeTab === 'application-analytics' && (
          <ApplicationAnalyticsTable />
        )}

        {/* System Analytics Tab Placeholder */}
        {activeTab === 'analytics' && (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">System Analytics</h3>
            <p className="text-gray-600">Detailed analytics and reporting features will be implemented here.</p>
          </div>
        )}
      </div>

      {/* Create User Modal */}
      {showCreateUserModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 sm:top-20 mx-auto p-4 sm:p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New User</h3>
              <button
                onClick={() => {
                  setShowCreateUserModal(false)
                  reset()
                }}
                className="btn btn-ghost btn-sm p-1 text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="label">First Name</label>
                  <input
                    {...register('firstName', {
                      required: 'First name is required',
                      minLength: { value: 2, message: 'Minimum 2 characters' }
                    })}
                    type="text"
                    className={`input ${errors.firstName ? 'input-error' : ''}`}
                    placeholder="John"
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <label className="label">Last Name</label>
                  <input
                    {...register('lastName', {
                      required: 'Last name is required',
                      minLength: { value: 2, message: 'Minimum 2 characters' }
                    })}
                    type="text"
                    className={`input ${errors.lastName ? 'input-error' : ''}`}
                    placeholder="Doe"
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="label">Email</label>
                <input
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: 'Invalid email address'
                    }
                  })}
                  type="email"
                  className={`input ${errors.email ? 'input-error' : ''}`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label className="label">Phone (Optional)</label>
                <input
                  {...register('phone', {
                    pattern: {
                      value: /^\+?[\d\s\-\(\)]+$/,
                      message: 'Invalid phone number'
                    }
                  })}
                  type="tel"
                  className={`input ${errors.phone ? 'input-error' : ''}`}
                  placeholder="+****************"
                />
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <label className="label">Role</label>
                <select
                  {...register('role', { required: 'Role is required' })}
                  className={`input ${errors.role ? 'input-error' : ''}`}
                >
                  <option value="">Select Role</option>
                  <option value="reviewer">Reviewer</option>
                  <option value="admin">Admin</option>
                </select>
                {errors.role && (
                  <p className="mt-1 text-sm text-red-600">{errors.role.message}</p>
                )}
              </div>

              <div>
                <label className="label">Password</label>
                <div className="relative">
                  <input
                    {...register('password', {
                      required: 'Password is required',
                      minLength: { value: 8, message: 'Minimum 8 characters' },
                      pattern: {
                        value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                        message: 'Password must contain uppercase, lowercase, number, and special character'
                      }
                    })}
                    type={showPassword ? 'text' : 'password'}
                    className={`input pr-10 ${errors.password ? 'input-error' : ''}`}
                    placeholder="Create password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center btn-ghost btn-sm text-gray-400 hover:text-gray-600"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
                )}
              </div>

              <div>
                <label className="label">Confirm Password</label>
                <input
                  {...register('confirmPassword', {
                    required: 'Please confirm password',
                    validate: (value) => value === password || 'Passwords do not match'
                  })}
                  type="password"
                  className={`input ${errors.confirmPassword ? 'input-error' : ''}`}
                  placeholder="Confirm password"
                />
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
                )}
              </div>

              <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateUserModal(false)
                    reset()
                  }}
                  className="btn btn-secondary w-full sm:w-auto order-2 sm:order-1"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn btn-primary w-full sm:w-auto order-1 sm:order-2"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      Creating...
                    </div>
                  ) : (
                    'Create User'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 sm:top-20 mx-auto p-4 sm:p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Edit User</h3>
              <button
                onClick={() => setSelectedUser(null)}
                className="btn btn-ghost btn-sm p-1 text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <EditUserFormContent
              user={selectedUser}
              onClose={() => setSelectedUser(null)}
              updateUserMutation={updateUserMutation}
            />
          </div>
        </div>
      )}
    </div>
  )
}