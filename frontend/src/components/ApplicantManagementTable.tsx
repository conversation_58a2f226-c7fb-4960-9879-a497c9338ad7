import { useState, useEffect } from 'react'
import { useQ<PERSON>y, useMutation, useQueryClient } from 'react-query'
import { useForm } from 'react-hook-form'
import {
  Users,
  Search,
  Edit,
  Trash2,
  User,
  Eye,
  X,
  DollarSign,
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Check,
  Square,
  CheckSquare,
  Download,
  RotateCcw,
  Trash,
  FileDown
} from 'lucide-react'
import { adminApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'
import toast from 'react-hot-toast'
import type { User as UserType } from '@/types'

interface ApplicantFilters {
  search: string
  status: string
  page: number
  limit: number
}

interface EditApplicantFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
}

function EditApplicantFormContent({ user, onClose, updateUserMutation }: {
  user: UserType
  onClose: () => void
  updateUserMutation: any
}) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<EditApplicantFormData>({
    defaultValues: {
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone || ''
    }
  })

  const onSubmit = async (data: EditApplicantFormData) => {
    try {
      await updateUserMutation.mutateAsync({ id: user.id, data })
      onClose()
    } catch (error) {
      // Error is handled by the mutation
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="label">First Name</label>
          <input
            {...register('firstName', {
              required: 'First name is required',
              minLength: { value: 2, message: 'Minimum 2 characters' }
            })}
            type="text"
            className={`input ${errors.firstName ? 'input-error' : ''}`}
            placeholder="John"
          />
          {errors.firstName && (
            <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
          )}
        </div>

        <div>
          <label className="label">Last Name</label>
          <input
            {...register('lastName', {
              required: 'Last name is required',
              minLength: { value: 2, message: 'Minimum 2 characters' }
            })}
            type="text"
            className={`input ${errors.lastName ? 'input-error' : ''}`}
            placeholder="Doe"
          />
          {errors.lastName && (
            <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="label">Email</label>
        <input
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Invalid email address'
            }
          })}
          type="email"
          className={`input ${errors.email ? 'input-error' : ''}`}
          placeholder="<EMAIL>"
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>

      <div>
        <label className="label">Phone (Optional)</label>
        <input
          {...register('phone', {
            pattern: {
              value: /^\+?[\d\s\-\(\)]+$/,
              message: 'Invalid phone number'
            }
          })}
          type="tel"
          className={`input ${errors.phone ? 'input-error' : ''}`}
          placeholder="+****************"
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
        )}
      </div>

      <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-secondary w-full sm:w-auto order-2 sm:order-1"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting || updateUserMutation.isLoading}
          className="btn btn-primary w-full sm:w-auto order-1 sm:order-2"
        >
          {isSubmitting || updateUserMutation.isLoading ? (
            <div className="flex items-center justify-center">
              <LoadingSpinner size="sm" className="mr-2" />
              Updating...
            </div>
          ) : (
            'Update Applicant'
          )}
        </button>
      </div>
    </form>
  )
}

export default function ApplicantManagementTable() {
  const [selectedApplicant, setSelectedApplicant] = useState<UserType | null>(null)
  const [applicantFilters, setApplicantFilters] = useState<ApplicantFilters>({
    search: '',
    status: '',
    page: 1,
    limit: 10
  })

  // Bulk operations state
  const [selectedApplications, setSelectedApplications] = useState<Set<string>>(new Set())
  const [showBulkStatusModal, setShowBulkStatusModal] = useState(false)
  const [bulkStatus, setBulkStatus] = useState('')
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false)

  const queryClient = useQueryClient()

  // Fetch applicants with their application data
  const { data: applicationsData, isLoading: applicationsLoading } = useQuery(
    ['admin-applications', applicantFilters],
    () => adminApi.getApplications({
      page: applicantFilters.page,
      limit: applicantFilters.limit,
      search: applicantFilters.search,
      status: applicantFilters.status || undefined
    }),
    {
      keepPreviousData: true,
      staleTime: 30000
    }
  )

  // Extract applications from the response data
  const applications = applicationsData?.data?.applications || []

  // Update applicant mutation
  const updateApplicantMutation = useMutation(
    ({ id, data }: { id: string; data: Partial<UserType> }) => adminApi.updateUser(id, data),
    {
      onSuccess: () => {
        toast.success('Applicant updated successfully')
        queryClient.invalidateQueries(['admin-applications'])
        setSelectedApplicant(null)
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to update applicant')
      }
    }
  )

  // Delete applicant mutation
  const deleteApplicantMutation = useMutation(adminApi.deleteUser, {
    onSuccess: () => {
      toast.success('Applicant deleted successfully')
      queryClient.invalidateQueries(['admin-applications'])
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete applicant')
    }
  })

  // Bulk operations mutations
  const bulkUpdateStatusMutation = useMutation(
    ({ applicationIds, status }: { applicationIds: string[]; status: string }) =>
      adminApi.bulkUpdateApplicationStatus(applicationIds, status),
    {
      onSuccess: (data) => {
        toast.success(`Successfully updated ${data.updatedCount} applications`)
        queryClient.invalidateQueries(['admin-applications'])
        setSelectedApplications(new Set())
        setShowBulkStatusModal(false)
        setBulkStatus('')
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to update applications')
      }
    }
  )

  const bulkDeleteMutation = useMutation(
    (applicationIds: string[]) => adminApi.bulkDeleteApplications(applicationIds),
    {
      onSuccess: (data) => {
        toast.success(`Successfully deleted ${data.deletedCount} applications`)
        queryClient.invalidateQueries(['admin-applications'])
        setSelectedApplications(new Set())
        setShowBulkDeleteConfirm(false)
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to delete applications')
      }
    }
  )

  const bulkExportMutation = useMutation(
    ({ applicationIds, format }: { applicationIds: string[]; format: 'csv' | 'json' }) =>
      adminApi.bulkExportApplications(applicationIds, format),
    {
      onSuccess: (data, variables) => {
        if (variables.format === 'csv') {
          // Create download link for CSV
          const url = window.URL.createObjectURL(new Blob([data]))
          const link = document.createElement('a')
          link.href = url
          const timestamp = new Date().toISOString().split('T')[0]
          link.setAttribute('download', `loan_applications_${timestamp}.csv`)
          document.body.appendChild(link)
          link.click()
          link.remove()
          window.URL.revokeObjectURL(url)
          toast.success(`Exported ${variables.applicationIds.length} applications to CSV`)
        } else {
          toast.success(`Exported ${data.totalRecords} applications`)
        }
      },
      onError: (error: any) => {
        toast.error(error.response?.data?.message || 'Failed to export applications')
      }
    }
  )

  const handleDeleteApplicant = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this applicant? This action cannot be undone and will also delete their applications.')) {
      await deleteApplicantMutation.mutateAsync(userId)
    }
  }

  // Bulk operations handlers
  const handleSelectApplication = (applicationId: string) => {
    const newSelected = new Set(selectedApplications)
    if (newSelected.has(applicationId)) {
      newSelected.delete(applicationId)
    } else {
      newSelected.add(applicationId)
    }
    setSelectedApplications(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedApplications.size === applications.length) {
      setSelectedApplications(new Set())
    } else {
      setSelectedApplications(new Set(applications.map((app: any) => app.application_id)))
    }
  }

  const handleClearSelection = () => {
    setSelectedApplications(new Set())
  }

  const handleBulkStatusUpdate = () => {
    if (selectedApplications.size === 0) return
    setShowBulkStatusModal(true)
  }

  const confirmBulkStatusUpdate = () => {
    if (!bulkStatus || selectedApplications.size === 0) return
    bulkUpdateStatusMutation.mutate({
      applicationIds: Array.from(selectedApplications),
      status: bulkStatus
    })
  }

  const handleBulkDelete = () => {
    if (selectedApplications.size === 0) return
    setShowBulkDeleteConfirm(true)
  }

  const confirmBulkDelete = () => {
    if (selectedApplications.size === 0) return
    bulkDeleteMutation.mutate(Array.from(selectedApplications))
  }

  const handleBulkExport = (format: 'csv' | 'json' = 'csv') => {
    if (selectedApplications.size === 0) return
    bulkExportMutation.mutate({
      applicationIds: Array.from(selectedApplications),
      format
    })
  }

  // Clear selection when filters change
  useEffect(() => {
    setSelectedApplications(new Set())
  }, [applicantFilters.search, applicantFilters.status, applicantFilters.page])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'new': { color: 'bg-blue-100 text-blue-800', icon: Clock, label: 'New' },
      'submitted': { color: 'bg-blue-100 text-blue-800', icon: Clock, label: 'Submitted' },
      'under_review': { color: 'bg-yellow-100 text-yellow-800', icon: AlertCircle, label: 'Under Review' },
      'approved': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Approved' },
      'declined': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Declined' },
      'rejected': { color: 'bg-red-100 text-red-800', icon: XCircle, label: 'Rejected' },
      'funded': { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Funded' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['new']
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {config.label}
      </span>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Application Management</h2>
          <p className="text-gray-600">Manage applicants and their loan applications</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search applicants..."
                value={applicantFilters.search}
                onChange={(e) => setApplicantFilters(prev => ({ ...prev, search: e.target.value, page: 1 }))}
                className="input pl-10"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Application Status</label>
            <select
              value={applicantFilters.status}
              onChange={(e) => setApplicantFilters(prev => ({ ...prev, status: e.target.value, page: 1 }))}
              className="input"
            >
              <option value="">All Statuses</option>
              <option value="new">New/Submitted</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="declined">Declined</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Per Page</label>
            <select
              value={applicantFilters.limit}
              onChange={(e) => setApplicantFilters(prev => ({ ...prev, limit: parseInt(e.target.value), page: 1 }))}
              className="input"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Actions Toolbar */}
      {selectedApplications.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <CheckSquare className="w-5 h-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">
                  {selectedApplications.size} application{selectedApplications.size !== 1 ? 's' : ''} selected
                </span>
              </div>
              <button
                onClick={handleClearSelection}
                className="text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Clear selection
              </button>
            </div>

            <div className="flex flex-wrap items-center gap-2">
              <button
                onClick={handleBulkStatusUpdate}
                disabled={bulkUpdateStatusMutation.isLoading}
                className="btn btn-sm btn-outline-primary"
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Change Status
              </button>

              <button
                onClick={() => handleBulkExport('csv')}
                disabled={bulkExportMutation.isLoading}
                className="btn btn-sm btn-outline-secondary"
              >
                <FileDown className="w-4 h-4 mr-1" />
                Export CSV
              </button>

              <button
                onClick={handleBulkDelete}
                disabled={bulkDeleteMutation.isLoading}
                className="btn btn-sm btn-outline-danger"
              >
                <Trash className="w-4 h-4 mr-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Applications Table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {applicationsLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : applications.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
            <p className="text-gray-600">No loan applications match your current filters.</p>
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center">
                        <button
                          onClick={handleSelectAll}
                          className="flex items-center justify-center w-4 h-4 border border-gray-300 rounded hover:bg-gray-100"
                          title={selectedApplications.size === applications.length ? 'Deselect all' : 'Select all'}
                        >
                          {selectedApplications.size === applications.length && applications.length > 0 ? (
                            <Check className="w-3 h-3 text-blue-600" />
                          ) : selectedApplications.size > 0 ? (
                            <div className="w-2 h-2 bg-blue-600 rounded-sm" />
                          ) : null}
                        </button>
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Applicant
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Application
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Dates
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {applications.map((app: any) => (
                    <tr
                      key={app.application_id}
                      className={`hover:bg-gray-50 ${selectedApplications.has(app.application_id) ? 'bg-blue-50' : ''}`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleSelectApplication(app.application_id)}
                          className="flex items-center justify-center w-4 h-4 border border-gray-300 rounded hover:bg-gray-100"
                          title={selectedApplications.has(app.application_id) ? 'Deselect application' : 'Select application'}
                        >
                          {selectedApplications.has(app.application_id) && (
                            <Check className="w-3 h-3 text-blue-600" />
                          )}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              <User className="h-5 w-5 text-gray-500" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {app.first_name} {app.last_name}
                            </div>
                            <div className="text-sm text-gray-500">{app.user_email}</div>
                            {app.phone && (
                              <div className="text-sm text-gray-500">{app.phone}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8">
                            <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                              <DollarSign className="h-4 w-4 text-blue-600" />
                            </div>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">
                              {formatCurrency(app.loan_amount)}
                            </div>
                            <div className="text-sm text-gray-500">{app.loan_purpose}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(app.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="space-y-1">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            <span className="text-xs">Created: {formatDate(app.application_created_at)}</span>
                          </div>
                          {app.submitted_at && (
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              <span className="text-xs">Submitted: {formatDate(app.submitted_at)}</span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => setSelectedApplicant({
                              id: app.user_id,
                              email: app.user_email,
                              firstName: app.first_name,
                              lastName: app.last_name,
                              phone: app.phone,
                              role: 'applicant',
                              emailVerified: app.email_verified,
                              phoneVerified: app.phone_verified || false,
                              createdAt: app.user_created_at,
                              lastLogin: null
                            })}
                            className="btn btn-sm btn-outline-primary"
                            title="Edit applicant"
                            aria-label="Edit applicant"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteApplicant(app.user_id)}
                            className="btn btn-sm btn-outline-danger"
                            title="Delete applicant"
                            aria-label="Delete applicant"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden">
              <div className="space-y-4 p-4">
                {applications.map((app: any) => (
                  <div
                    key={app.application_id}
                    className={`rounded-lg p-4 space-y-3 ${selectedApplications.has(app.application_id) ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'}`}
                  >
                    {/* Selection and Applicant Info */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => handleSelectApplication(app.application_id)}
                          className="flex items-center justify-center w-5 h-5 border border-gray-300 rounded hover:bg-gray-100 mt-1"
                          title={selectedApplications.has(app.application_id) ? 'Deselect application' : 'Select application'}
                        >
                          {selectedApplications.has(app.application_id) && (
                            <Check className="w-3 h-3 text-blue-600" />
                          )}
                        </button>
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <User className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {app.first_name} {app.last_name}
                          </div>
                          <div className="text-sm text-gray-500">{app.user_email}</div>
                          {app.phone && (
                            <div className="text-sm text-gray-500">{app.phone}</div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => setSelectedApplicant({
                            id: app.user_id,
                            email: app.user_email,
                            firstName: app.first_name,
                            lastName: app.last_name,
                            phone: app.phone,
                            role: 'applicant',
                            emailVerified: app.email_verified,
                            phoneVerified: app.phone_verified || false,
                            createdAt: app.user_created_at,
                            lastLogin: null
                          })}
                          className="btn btn-sm btn-outline-primary"
                          title="Edit applicant"
                          aria-label="Edit applicant"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteApplicant(app.user_id)}
                          className="btn btn-sm btn-outline-danger"
                          title="Delete applicant"
                          aria-label="Delete applicant"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Application Info */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wider">Loan Amount</div>
                        <div className="text-sm font-medium text-gray-900 flex items-center">
                          <DollarSign className="h-4 w-4 text-blue-600 mr-1" />
                          {formatCurrency(app.loan_amount)}
                        </div>
                        <div className="text-xs text-gray-500">{app.loan_purpose}</div>
                      </div>
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wider">Status</div>
                        <div className="mt-1">
                          {getStatusBadge(app.status)}
                        </div>
                      </div>
                    </div>

                    {/* Dates */}
                    <div className="space-y-1">
                      <div className="flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>Created: {formatDate(app.application_created_at)}</span>
                      </div>
                      {app.submitted_at && (
                        <div className="flex items-center text-xs text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          <span>Submitted: {formatDate(app.submitted_at)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Pagination */}
        {applicationsData?.pagination && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setApplicantFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={!applicationsData.pagination.hasPrev}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </button>
              <button
                onClick={() => setApplicantFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={!applicationsData.pagination.hasNext}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing{' '}
                  <span className="font-medium">
                    {((applicationsData.pagination.page - 1) * applicantFilters.limit) + 1}
                  </span>{' '}
                  to{' '}
                  <span className="font-medium">
                    {Math.min(
                      applicationsData.pagination.page * applicantFilters.limit,
                      applicationsData.pagination.total || 0
                    )}
                  </span>{' '}
                  of{' '}
                  <span className="font-medium">{applicationsData.pagination.total || 0}</span>{' '}
                  results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => setApplicantFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                    disabled={!applicationsData.pagination.hasPrev}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                  </button>
                  <button
                    onClick={() => setApplicantFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                    disabled={!applicationsData.pagination.hasNext}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" aria-hidden="true" />
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Edit Applicant Modal */}
      {selectedApplicant && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 sm:top-20 mx-auto p-4 sm:p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Edit Applicant</h3>
              <button
                onClick={() => setSelectedApplicant(null)}
                className="btn btn-ghost btn-sm p-1 text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <EditApplicantFormContent
              user={selectedApplicant}
              onClose={() => setSelectedApplicant(null)}
              updateUserMutation={updateApplicantMutation}
            />
          </div>
        </div>
      )}

      {/* Bulk Status Update Modal */}
      {showBulkStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 sm:top-20 mx-auto p-4 sm:p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Change Status</h3>
              <button
                onClick={() => {
                  setShowBulkStatusModal(false)
                  setBulkStatus('')
                }}
                className="btn btn-ghost btn-sm p-1 text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Update the status for {selectedApplications.size} selected application{selectedApplications.size !== 1 ? 's' : ''}:
              </p>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                <select
                  value={bulkStatus}
                  onChange={(e) => setBulkStatus(e.target.value)}
                  className="input w-full"
                >
                  <option value="">Select status...</option>
                  <option value="new">New/Submitted</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="declined">Declined</option>
                </select>
              </div>

              <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4">
                <button
                  onClick={() => {
                    setShowBulkStatusModal(false)
                    setBulkStatus('')
                  }}
                  className="btn btn-secondary w-full sm:w-auto order-2 sm:order-1"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmBulkStatusUpdate}
                  disabled={!bulkStatus || bulkUpdateStatusMutation.isLoading}
                  className="btn btn-primary w-full sm:w-auto order-1 sm:order-2"
                >
                  {bulkUpdateStatusMutation.isLoading ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      Updating...
                    </div>
                  ) : (
                    'Update Status'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 p-4">
          <div className="relative top-4 sm:top-20 mx-auto p-4 sm:p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Confirm Deletion</h3>
              <button
                onClick={() => setShowBulkDeleteConfirm(false)}
                className="btn btn-ghost btn-sm p-1 text-gray-400 hover:text-gray-600"
                aria-label="Close modal"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-900">
                    Are you sure you want to delete {selectedApplications.size} selected application{selectedApplications.size !== 1 ? 's' : ''}?
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    This action cannot be undone and will permanently remove the application data.
                  </p>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4">
                <button
                  onClick={() => setShowBulkDeleteConfirm(false)}
                  className="btn btn-secondary w-full sm:w-auto order-2 sm:order-1"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmBulkDelete}
                  disabled={bulkDeleteMutation.isLoading}
                  className="btn btn-danger w-full sm:w-auto order-1 sm:order-2"
                >
                  {bulkDeleteMutation.isLoading ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      Deleting...
                    </div>
                  ) : (
                    'Delete Applications'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
