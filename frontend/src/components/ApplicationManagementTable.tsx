import { useState, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import {
  Search,
  Eye,
  DollarSign,
  User,
  Calendar,
  ArrowUpDown,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
  Check,
  X,
  Trash2,
  Edit3,
  CalendarDays
} from 'lucide-react'
import { adminApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'

interface ApplicationManagementTableProps {
  className?: string
}

const ApplicationManagementTable: React.FC<ApplicationManagementTableProps> = ({ className = '' }) => {
  const [currentPage, setCurrentPage] = useState(1)
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState<'ASC' | 'DESC'>('DESC')
  const [selectedApplication, setSelectedApplication] = useState<any>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  // Bulk operations state
  const [selectedApplications, setSelectedApplications] = useState<Set<string>>(new Set())
  const [showBulkActions, setShowBulkActions] = useState(false)
  const [bulkStatus, setBulkStatus] = useState('')
  const [showBulkStatusModal, setShowBulkStatusModal] = useState(false)

  // Date filtering state
  const [dateField, setDateField] = useState('created')
  const [dateFrom, setDateFrom] = useState('')
  const [dateTo, setDateTo] = useState('')
  const [showDateFilters, setShowDateFilters] = useState(false)

  const queryClient = useQueryClient()

  // Fetch applications
  const { data, isLoading, error, refetch } = useQuery(
    ['admin-applications', currentPage, search, statusFilter, sortBy, sortOrder, dateField, dateFrom, dateTo],
    () => adminApi.getApplications({
      page: currentPage,
      limit: 20,
      search,
      status: statusFilter || undefined,
      sortBy,
      sortOrder,
      dateField: dateField || undefined,
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined
    }),
    {
      keepPreviousData: true,
      staleTime: 30000
    }
  )

  // Update application status mutation
  const updateStatusMutation = useMutation(
    ({ id, status }: { id: string; status: string }) => adminApi.updateApplicationStatus(id, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['admin-applications'])
      },
      onError: (error: any) => {
        console.error('Failed to update application status:', error)
        alert('Failed to update application status. Please try again.')
      }
    }
  )

  // Bulk status update mutation
  const bulkUpdateStatusMutation = useMutation(
    ({ applicationIds, status }: { applicationIds: string[]; status: string }) =>
      adminApi.bulkUpdateApplicationStatus(applicationIds, status),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['admin-applications'])
        setSelectedApplications(new Set())
        setShowBulkStatusModal(false)
        setBulkStatus('')
      },
      onError: (error: any) => {
        console.error('Failed to bulk update application status:', error)
        alert('Failed to update application statuses. Please try again.')
      }
    }
  )

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation(
    (applicationIds: string[]) => adminApi.bulkDeleteApplications(applicationIds),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['admin-applications'])
        setSelectedApplications(new Set())
      },
      onError: (error: any) => {
        console.error('Failed to bulk delete applications:', error)
        alert('Failed to delete applications. Please try again.')
      }
    }
  )

  const handleStatusChange = (applicationId: string, newStatus: string) => {
    updateStatusMutation.mutate({ id: applicationId, status: newStatus })
  }

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'ASC' ? 'DESC' : 'ASC')
    } else {
      setSortBy(column)
      setSortOrder('DESC')
    }
  }

  // Bulk operations handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(applications.map((app: any) => app.application_id))
      setSelectedApplications(allIds)
    } else {
      setSelectedApplications(new Set())
    }
  }

  const handleSelectApplication = (applicationId: string, checked: boolean) => {
    const newSelected = new Set(selectedApplications)
    if (checked) {
      newSelected.add(applicationId)
    } else {
      newSelected.delete(applicationId)
    }
    setSelectedApplications(newSelected)
  }

  const handleBulkStatusUpdate = () => {
    if (selectedApplications.size === 0 || !bulkStatus) return

    const applicationIds = Array.from(selectedApplications)
    bulkUpdateStatusMutation.mutate({ applicationIds, status: bulkStatus })
  }

  const handleBulkDelete = () => {
    if (selectedApplications.size === 0) return

    if (confirm(`Are you sure you want to delete ${selectedApplications.size} application(s)? This action cannot be undone.`)) {
      const applicationIds = Array.from(selectedApplications)
      bulkDeleteMutation.mutate(applicationIds)
    }
  }

  const handleDownloadSelected = async () => {
    if (selectedApplications.size === 0) return

    try {
      const applicationIds = Array.from(selectedApplications)
      const response = await adminApi.exportApplications(applicationIds)

      // Create download link
      const blob = new Blob([response.data], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `applications_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download applications:', error)
      alert('Failed to download applications. Please try again.')
    }
  }

  // Date filtering handlers
  const handleDatePreset = (preset: string) => {
    const today = new Date()
    const formatDate = (date: Date) => date.toISOString().split('T')[0]

    switch (preset) {
      case 'today':
        setDateFrom(formatDate(today))
        setDateTo(formatDate(today))
        break
      case 'yesterday':
        const yesterday = new Date(today)
        yesterday.setDate(yesterday.getDate() - 1)
        setDateFrom(formatDate(yesterday))
        setDateTo(formatDate(yesterday))
        break
      case 'last7days':
        const last7 = new Date(today)
        last7.setDate(last7.getDate() - 7)
        setDateFrom(formatDate(last7))
        setDateTo(formatDate(today))
        break
      case 'last30days':
        const last30 = new Date(today)
        last30.setDate(last30.getDate() - 30)
        setDateFrom(formatDate(last30))
        setDateTo(formatDate(today))
        break
      case 'thisMonth':
        const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        setDateFrom(formatDate(thisMonthStart))
        setDateTo(formatDate(today))
        break
      case 'lastMonth':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)
        setDateFrom(formatDate(lastMonthStart))
        setDateTo(formatDate(lastMonthEnd))
        break
    }
    setCurrentPage(1) // Reset pagination when filters change
  }

  const clearDateFilters = () => {
    setDateField('created')
    setDateFrom('')
    setDateTo('')
    setCurrentPage(1)
  }

  const clearAllFilters = () => {
    setSearch('')
    setStatusFilter('')
    setDateField('created')
    setDateFrom('')
    setDateTo('')
    setCurrentPage(1)
  }

  // Effect to show/hide bulk actions
  useEffect(() => {
    setShowBulkActions(selectedApplications.size > 0)
  }, [selectedApplications])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      new: { color: 'bg-blue-100 text-blue-800', label: 'New' },
      under_review: { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved' },
      declined: { color: 'bg-red-100 text-red-800', label: 'Declined' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.new
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    console.error('ApplicationManagementTable error:', error)
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Applications</h3>
          <p className="text-red-600 mb-4">
            {error?.response?.data?.message || error?.message || 'Failed to load applications data'}
          </p>
          <button
            onClick={() => refetch()}
            className="btn btn-warning"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const applications = data?.data?.applications || []
  const pagination = data?.data?.pagination || {}

  // Show empty state if no applications
  if (!isLoading && applications.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Applications Found</h3>
          <p className="text-gray-600 mb-4">
            {search || statusFilter ? 'No applications match your current filters.' : 'There are no applications to display.'}
          </p>
          {(search || statusFilter) && (
            <button
              onClick={() => {
                setSearch('')
                setStatusFilter('')
              }}
              className="btn btn-info"
            >
              Clear Filters
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm w-full ${className}`}>
      {/* Header with Search and Filters */}
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Application Management</h2>
              <p className="text-sm text-gray-600 mt-1">
                Review and manage loan applications with status updates
                {pagination.totalApplications && (
                  <span className="ml-2 text-blue-600 font-medium">
                    ({pagination.totalApplications} total)
                  </span>
                )}
              </p>
            </div>

            {/* Date Filter Toggle */}
            <button
              onClick={() => setShowDateFilters(!showDateFilters)}
              className={`btn btn-sm ${showDateFilters ? 'btn-primary' : 'btn-outline-secondary'}`}
            >
              <CalendarDays className="h-4 w-4 mr-2" />
              Date Filters
            </button>
          </div>
        </div>

        {/* Date Filters Section */}
        {showDateFilters && (
          <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
            <div className="flex flex-col gap-4">
              {/* Date Field Selection */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="sm:w-48">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Filter by</label>
                  <select
                    value={dateField}
                    onChange={(e) => setDateField(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="created">Application Created</option>
                    <option value="submitted">Application Submitted</option>
                    <option value="reviewed">Application Reviewed</option>
                  </select>
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                  <input
                    type="date"
                    value={dateFrom}
                    onChange={(e) => setDateFrom(e.target.value)}
                    max={dateTo || undefined}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                  <input
                    type="date"
                    value={dateTo}
                    onChange={(e) => setDateTo(e.target.value)}
                    min={dateFrom || undefined}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Quick Date Presets */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Quick Presets</label>
                <div className="flex flex-wrap gap-2">
                  <button onClick={() => handleDatePreset('today')} className="btn btn-sm btn-outline-secondary">Today</button>
                  <button onClick={() => handleDatePreset('yesterday')} className="btn btn-sm btn-outline-secondary">Yesterday</button>
                  <button onClick={() => handleDatePreset('last7days')} className="btn btn-sm btn-outline-secondary">Last 7 Days</button>
                  <button onClick={() => handleDatePreset('last30days')} className="btn btn-sm btn-outline-secondary">Last 30 Days</button>
                  <button onClick={() => handleDatePreset('thisMonth')} className="btn btn-sm btn-outline-secondary">This Month</button>
                  <button onClick={() => handleDatePreset('lastMonth')} className="btn btn-sm btn-outline-secondary">Last Month</button>
                </div>
              </div>

              {/* Date Filter Actions */}
              <div className="flex gap-2">
                <button onClick={clearDateFilters} className="btn btn-sm btn-outline-warning">
                  Clear Date Filters
                </button>
                <button onClick={clearAllFilters} className="btn btn-sm btn-outline-danger">
                  Clear All Filters
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search applications..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 w-full"
              />
            </div>

            {/* Status Filter */}
            <div className="relative sm:w-48">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white w-full"
              >
                <option value="">All Statuses</option>
                <option value="new">New</option>
                <option value="under_review">Under Review</option>
                <option value="approved">Approved</option>
                <option value="declined">Declined</option>
              </select>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
            <button
              onClick={handleDownloadSelected}
              disabled={selectedApplications.size === 0}
              className="btn btn-success w-full sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Selected ({selectedApplications.size})
            </button>
          </div>
        </div>

        {/* Bulk Actions Floating Toolbar */}
        {showBulkActions && (
          <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-40">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">
                {selectedApplications.size} selected
              </span>

              <button
                onClick={() => setShowBulkStatusModal(true)}
                className="btn btn-sm btn-primary"
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Update Status
              </button>

              <button
                onClick={handleDownloadSelected}
                className="btn btn-sm btn-success"
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </button>

              <button
                onClick={handleBulkDelete}
                className="btn btn-sm btn-danger"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </button>

              <button
                onClick={() => setSelectedApplications(new Set())}
                className="btn btn-sm btn-outline-secondary"
              >
                <X className="h-4 w-4 mr-1" />
                Clear
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                <input
                  type="checkbox"
                  checked={applications.length > 0 && selectedApplications.size === applications.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('created_at')}
                  className="btn-sort flex items-center space-x-1"
                  aria-label="Sort by application date"
                >
                  <span>Application</span>
                  <ArrowUpDown className="h-3 w-3" />
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('loan_amount')}
                  className="btn-sort flex items-center space-x-1"
                  aria-label="Sort by loan amount"
                >
                  <span>Loan Details</span>
                  <ArrowUpDown className="h-3 w-3" />
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <button
                  onClick={() => handleSort('status')}
                  className="btn-sort flex items-center space-x-1"
                  aria-label="Sort by status"
                >
                  <span>Status</span>
                  <ArrowUpDown className="h-3 w-3" />
                </button>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timeline
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {applications.map((app: any) => (
              <tr key={app.application_id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedApplications.has(app.application_id)}
                    onChange={(e) => handleSelectApplication(app.application_id, e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        #{app.application_id.slice(0, 8)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatDate(app.application_created_at)}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                        <User className="h-4 w-4 text-gray-500" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {app.first_name} {app.last_name}
                      </div>
                      <div className="text-sm text-gray-500">{app.user_email}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 font-medium">
                    {formatCurrency(app.loan_amount)}
                  </div>
                  <div className="text-sm text-gray-500 capitalize">
                    {app.loan_purpose?.replace(/_/g, ' ')}
                  </div>
                  <div className="text-xs text-gray-400">
                    {app.employment_status}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <select
                    value={app.status}
                    onChange={(e) => handleStatusChange(app.application_id, e.target.value)}
                    disabled={updateStatusMutation.isLoading}
                    className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="new">New</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="declined">Declined</option>
                  </select>
                  <div className="mt-1">
                    {getStatusBadge(app.status)}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="space-y-1">
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span className="text-xs">Created: {formatDate(app.application_created_at)}</span>
                    </div>
                    {app.submitted_at && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className="text-xs">Submitted: {formatDate(app.submitted_at)}</span>
                      </div>
                    )}
                    {app.reviewed_at && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className="text-xs">Reviewed: {formatDate(app.reviewed_at)}</span>
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => {
                      setSelectedApplication(app)
                      setShowDetailsModal(true)
                    }}
                    className="btn btn-sm btn-outline-info"
                    aria-label="View application details"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden">
        <div className="divide-y divide-gray-200">
          {applications.map((app: any) => (
            <div key={app.application_id} className="p-4 hover:bg-gray-50">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      #{app.application_id.slice(0, 8)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatDate(app.application_created_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(app.status)}
                  <button
                    onClick={() => {
                      setSelectedApplication(app)
                      setShowDetailsModal(true)
                    }}
                    className="btn btn-sm btn-outline-info btn-touch"
                    title="View Details"
                    aria-label="View application details"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 h-8 w-8">
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <User className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {app.first_name} {app.last_name}
                    </p>
                    <p className="text-gray-500 truncate">{app.user_email}</p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <span className="text-gray-500">Amount:</span>
                    <span className="ml-1 font-medium text-gray-900">
                      {formatCurrency(app.loan_amount)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Purpose:</span>
                    <span className="ml-1 text-gray-900 capitalize">
                      {app.loan_purpose?.replace(/_/g, ' ')}
                    </span>
                  </div>
                </div>

                <div>
                  <span className="text-gray-500">Status:</span>
                  <select
                    value={app.status}
                    onChange={(e) => handleStatusChange(app.application_id, e.target.value)}
                    disabled={updateStatusMutation.isLoading}
                    className="ml-2 text-sm border border-gray-300 rounded px-2 py-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="new">New</option>
                    <option value="under_review">Under Review</option>
                    <option value="approved">Approved</option>
                    <option value="declined">Declined</option>
                  </select>
                </div>

                {(app.submitted_at || app.reviewed_at) && (
                  <div className="text-xs text-gray-500 space-y-1">
                    {app.submitted_at && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>Submitted: {formatDate(app.submitted_at)}</span>
                      </div>
                    )}
                    {app.reviewed_at && (
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span>Reviewed: {formatDate(app.reviewed_at)}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={!pagination.hasPrev}
              className="btn btn-outline-secondary"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
              disabled={!pagination.hasNext}
              className="btn btn-outline-secondary"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{((currentPage - 1) * 20) + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(currentPage * 20, pagination.totalApplications)}
                </span>{' '}
                of <span className="font-medium">{pagination.totalApplications}</span> applications
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <div className="btn-group">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={!pagination.hasPrev}
                    className="btn btn-outline-secondary"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>
                  <span className="btn btn-outline-light cursor-default">
                    Page {currentPage} of {pagination.totalPages}
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(pagination.totalPages, currentPage + 1))}
                    disabled={!pagination.hasNext}
                    className="btn btn-outline-secondary"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Application Details Modal - Placeholder for now */}
      {showDetailsModal && selectedApplication && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Application Details - #{selectedApplication.application_id.slice(0, 8)}
                </h3>
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Personal Information</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Name:</span> {selectedApplication.first_name} {selectedApplication.last_name}</p>
                    <p><span className="font-medium">Email:</span> {selectedApplication.user_email}</p>
                    <p><span className="font-medium">Phone:</span> {selectedApplication.phone || 'Not provided'}</p>
                    <p><span className="font-medium">Employment:</span> {selectedApplication.employment_status}</p>
                    <p><span className="font-medium">Annual Income:</span> {formatCurrency(selectedApplication.annual_income || 0)}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Loan Information</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Amount:</span> {formatCurrency(selectedApplication.loan_amount)}</p>
                    <p><span className="font-medium">Purpose:</span> {selectedApplication.loan_purpose?.replace(/_/g, ' ')}</p>
                    <p><span className="font-medium">Term:</span> {selectedApplication.loan_term || 'Not specified'}</p>
                    <p><span className="font-medium">Status:</span> {getStatusBadge(selectedApplication.status)}</p>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h4 className="font-medium text-gray-900 mb-2">Timeline</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Created:</span> {formatDate(selectedApplication.application_created_at)}</p>
                  {selectedApplication.submitted_at && (
                    <p><span className="font-medium">Submitted:</span> {formatDate(selectedApplication.submitted_at)}</p>
                  )}
                  {selectedApplication.reviewed_at && (
                    <p><span className="font-medium">Reviewed:</span> {formatDate(selectedApplication.reviewed_at)}</p>
                  )}
                  {selectedApplication.approved_at && (
                    <p><span className="font-medium">Approved:</span> {formatDate(selectedApplication.approved_at)}</p>
                  )}
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowDetailsModal(false)}
                  className="btn btn-secondary"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Status Update Modal */}
      {showBulkStatusModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Update Status for {selectedApplications.size} Application(s)
                </h3>
                <button
                  onClick={() => setShowBulkStatusModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Status
                </label>
                <select
                  value={bulkStatus}
                  onChange={(e) => setBulkStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Status</option>
                  <option value="new">New</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="declined">Declined</option>
                </select>
              </div>

              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setShowBulkStatusModal(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBulkStatusUpdate}
                  disabled={!bulkStatus || bulkUpdateStatusMutation.isLoading}
                  className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {bulkUpdateStatusMutation.isLoading ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Update Status
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default ApplicationManagementTable
