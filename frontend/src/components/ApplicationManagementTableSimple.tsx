import React from 'react'
import { useQuery } from 'react-query'
import { adminApi } from '@/lib/api'
import LoadingSpinner from '@/components/LoadingSpinner'

const ApplicationManagementTableSimple: React.FC = () => {
  const { data, isLoading, error } = useQuery(
    'admin-applications-simple',
    () => adminApi.getApplications(),
    {
      retry: 1,
      staleTime: 30000
    }
  )

  console.log('ApplicationManagementTableSimple - Data:', data)
  console.log('ApplicationManagementTableSimple - Loading:', isLoading)
  console.log('ApplicationManagementTableSimple - Error:', error)

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Loading applications...</span>
      </div>
    )
  }

  if (error) {
    console.error('ApplicationManagementTableSimple error:', error)
    return (
      <div className="bg-white rounded-lg shadow-sm p-8">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Applications</h3>
          <p className="text-red-600 mb-4">
            {(error as any)?.response?.data?.message || (error as any)?.message || 'Failed to load applications data'}
          </p>
          <pre className="text-xs text-gray-500 bg-gray-100 p-2 rounded">
            {JSON.stringify(error, null, 2)}
          </pre>
        </div>
      </div>
    )
  }

  const applications = data?.data?.applications || []
  console.log('Applications array:', applications)

  return (
    <div className="bg-white rounded-lg shadow-sm w-full">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-900">Application Management (Simple Test)</h2>
        <p className="text-sm text-gray-600 mt-1">
          Testing basic application loading - Found {applications.length} applications
        </p>
      </div>

      <div className="p-6">
        {applications.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No applications found</p>
            <pre className="text-xs text-gray-400 mt-2">
              Raw data: {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Successfully loaded {applications.length} applications:
            </p>
            <div className="grid gap-4">
              {applications.slice(0, 3).map((app: any, index: number) => (
                <div key={app.application_id || index} className="border rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>ID:</strong> {app.application_id?.slice(0, 8) || 'N/A'}
                    </div>
                    <div>
                      <strong>Status:</strong> {app.status || 'N/A'}
                    </div>
                    <div>
                      <strong>User:</strong> {app.first_name} {app.last_name}
                    </div>
                    <div>
                      <strong>Amount:</strong> ${app.loan_amount?.toLocaleString() || 'N/A'}
                    </div>
                  </div>
                </div>
              ))}
              {applications.length > 3 && (
                <p className="text-sm text-gray-500">
                  ... and {applications.length - 3} more applications
                </p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ApplicationManagementTableSimple
